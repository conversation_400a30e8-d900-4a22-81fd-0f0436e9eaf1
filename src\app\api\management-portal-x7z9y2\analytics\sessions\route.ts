/**
 * Session Analytics API Endpoint
 * 
 * This endpoint provides session-based analytics data for the admin dashboard.
 * Requires authentication and provides session metrics and insights.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logError, logInfo } from '@/lib/logging';
import { analyticsCache, CacheKeys, CACHE_TTL } from '@/lib/analytics/cache';

/**
 * Get time range filter based on timeRange parameter
 */
function getTimeRangeFilter(timeRange: string): { startDate: Date; endDate: Date } {
  const now = new Date();
  const endDate = new Date(now);
  let startDate: Date;

  switch (timeRange) {
    case '1h':
      startDate = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour ago
      break;
    case '24h':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago
      break;
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
      break;
    default:
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // Default to 24 hours
  }

  return { startDate, endDate };
}

/**
 * GET /api/management-portal-x7z9y2/analytics/sessions
 * Retrieve session analytics data
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange');
    const customStartDate = searchParams.get('startDate');
    const customEndDate = searchParams.get('endDate');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const sortBy = searchParams.get('sortBy') || 'session_start_time';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const minDuration = searchParams.get('minDuration');
    const maxDuration = searchParams.get('maxDuration');

    // Get time range - either from custom dates or timeRange parameter
    let startDate: string, endDate: string;
    if (customStartDate && customEndDate) {
      startDate = customStartDate;
      endDate = customEndDate;
    } else if (timeRange) {
      const { startDate: calculatedStart, endDate: calculatedEnd } = getTimeRangeFilter(timeRange);
      startDate = calculatedStart.toISOString();
      endDate = calculatedEnd.toISOString();
    } else {
      // Default to 24 hours if no time range specified
      const { startDate: calculatedStart, endDate: calculatedEnd } = getTimeRangeFilter('24h');
      startDate = calculatedStart.toISOString();
      endDate = calculatedEnd.toISOString();
    }

    // Validate parameters
    if (limit > 1000) {
      return NextResponse.json(
        { success: false, error: 'Limit cannot exceed 1000' },
        { status: 400 }
      );
    }

    if (startDate && isNaN(Date.parse(startDate))) {
      return NextResponse.json(
        { success: false, error: 'Invalid startDate format' },
        { status: 400 }
      );
    }

    if (endDate && isNaN(Date.parse(endDate))) {
      return NextResponse.json(
        { success: false, error: 'Invalid endDate format' },
        { status: 400 }
      );
    }

    // Generate cache key for session analytics
    const cacheKey = customStartDate && customEndDate
      ? CacheKeys.customRange(customStartDate, customEndDate, `sessions-${limit}-${sortBy}-${sortOrder}`)
      : CacheKeys.sessionAnalytics(timeRange || '24h', limit);

    // Try to get from cache first
    const cachedResult = analyticsCache.get<{
      data: any;
      meta: any;
      cacheTime: string;
    }>(cacheKey);
    if (cachedResult) {
      logInfo('SessionAnalyticsAPI', 'Cache hit for session analytics', {
        cacheKey,
        timeRange: timeRange || '24h',
        limit
      });
      return NextResponse.json({
        success: true,
        data: cachedResult.data,
        meta: {
          ...cachedResult.meta,
          cached: true,
          cacheTime: cachedResult.cacheTime
        }
      });
    }

    const supabase = createServerSupabaseClient();

    // Build the query for session_analytics table
    let query = supabase
      .from('session_analytics')
      .select('*')
      .gte('session_start_time', startDate)
      .lte('session_start_time', endDate);

    // Apply duration filters if provided
    if (minDuration) {
      query = query.gte('session_duration_seconds', parseInt(minDuration));
    }
    if (maxDuration) {
      query = query.lte('session_duration_seconds', parseInt(maxDuration));
    }

    // Apply sorting
    const isValidSortColumn = ['session_start_time', 'session_duration', 'emails_generated_count', 'emails_received_count'].includes(sortBy);
    if (isValidSortColumn) {
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });
    } else {
      query = query.order('session_start_time', { ascending: false });
    }

    // Get total count first (before pagination)
    const countQuery = supabase
      .from('session_analytics')
      .select('*', { count: 'exact', head: true });

    // Apply same time filtering to count query
    if (timeRange && timeRange !== 'all') {
      const { startDate, endDate } = getTimeRangeFilter(timeRange);
      countQuery.gte('session_start_time', startDate.toISOString());
      countQuery.lte('session_start_time', endDate.toISOString());
    }

    const { count: totalSessionsCount, error: countError } = await countQuery;

    if (countError) {
      logError('SessionAnalyticsAPI', 'Error counting session data', { error: countError });
      return NextResponse.json(
        { success: false, error: 'Failed to count session data' },
        { status: 500 }
      );
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: sessions, error } = await query;

    if (error) {
      logError('SessionAnalyticsAPI', 'Error fetching session data', { error });
      return NextResponse.json(
        { success: false, error: 'Failed to fetch session data' },
        { status: 500 }
      );
    }

    // Use actual total count, not just paginated results
    const totalSessions = totalSessionsCount || 0;
    let totalEmailsGenerated = 0;
    let totalEmailsReceived = 0;
    let totalEmailsViewed = 0;
    let totalEmailsDeleted = 0;
    let totalCopyActions = 0;
    let totalManualRefreshes = 0;
    let totalSessionDuration = 0;

    sessions?.forEach(session => {
      totalEmailsGenerated += session.emails_generated_count || 0;
      totalEmailsReceived += session.emails_received_count || 0;
      totalEmailsViewed += session.emails_viewed_count || 0;
      totalEmailsDeleted += session.emails_deleted_count || 0;
      totalCopyActions += session.copy_actions_count || 0;
      totalManualRefreshes += session.manual_refresh_count || 0;

      // Calculate duration for active sessions or use stored duration
      let sessionDuration = session.session_duration || 0;
      if (!sessionDuration && session.session_start_time) {
        const startTime = new Date(session.session_start_time);
        const endTime = session.session_end_time ? new Date(session.session_end_time) : new Date();
        sessionDuration = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);
      }
      totalSessionDuration += sessionDuration;
    });

    const avgSessionDuration = totalSessions > 0 ? totalSessionDuration / totalSessions : 0;
    const avgEmailsGeneratedPerSession = totalSessions > 0 ? totalEmailsGenerated / totalSessions : 0;
    const avgEmailsReceivedPerSession = totalSessions > 0 ? totalEmailsReceived / totalSessions : 0;
    const avgEmailsViewedPerSession = totalSessions > 0 ? totalEmailsViewed / totalSessions : 0;
    const avgEmailsDeletedPerSession = totalSessions > 0 ? totalEmailsDeleted / totalSessions : 0;
    const avgCopyActionsPerSession = totalSessions > 0 ? totalCopyActions / totalSessions : 0;
    const avgManualRefreshesPerSession = totalSessions > 0 ? totalManualRefreshes / totalSessions : 0;
    const emailViewRate = totalEmailsReceived > 0 ? (totalEmailsViewed / totalEmailsReceived) * 100 : 0;
    const emailDeleteRate = totalEmailsGenerated > 0 ? (totalEmailsDeleted / totalEmailsGenerated) * 100 : 0;

    // Calculate breakdowns
    const deviceBreakdown: Record<string, number> = {};
    const browserBreakdown: Record<string, number> = {};
    const countryBreakdown: Record<string, number> = {};
    const durationBreakdown: Record<string, number> = {
      '0-1m': 0,
      '1-5m': 0,
      '5-15m': 0,
      '15m+': 0
    };

    sessions?.forEach(session => {
      // Device breakdown
      const device = session.device_type || 'unknown';
      deviceBreakdown[device] = (deviceBreakdown[device] || 0) + 1;

      // Browser breakdown
      const browser = session.browser || 'unknown';
      browserBreakdown[browser] = (browserBreakdown[browser] || 0) + 1;

      // Country breakdown
      const country = session.country || 'unknown';
      countryBreakdown[country] = (countryBreakdown[country] || 0) + 1;

      // Duration breakdown (calculate for active sessions)
      let duration = session.session_duration || 0;
      if (!duration && session.session_start_time) {
        const startTime = new Date(session.session_start_time);
        const endTime = session.session_end_time ? new Date(session.session_end_time) : new Date();
        duration = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);
      }

      if (duration <= 60) {
        durationBreakdown['0-1m']++;
      } else if (duration <= 300) {
        durationBreakdown['1-5m']++;
      } else if (duration <= 900) {
        durationBreakdown['5-15m']++;
      } else {
        durationBreakdown['15m+']++;
      }
    });

    // Prepare response data
    const responseData = {
      data: {
        summary: {
          totalSessions,
          totalEmailsGenerated,
          totalEmailsReceived,
          totalEmailsViewed,
          totalEmailsDeleted,
          totalCopyActions,
          totalManualRefreshes,
          avgSessionDuration
        },
        engagement: {
          avgEmailsGeneratedPerSession,
          avgEmailsReceivedPerSession,
          avgEmailsViewedPerSession,
          avgEmailsDeletedPerSession,
          avgCopyActionsPerSession,
          avgManualRefreshesPerSession,
          emailViewRate,
          emailDeleteRate
        },
        breakdowns: {
          device: deviceBreakdown,
          browser: browserBreakdown,
          country: countryBreakdown,
          sessionDuration: durationBreakdown
        },
        sessions: sessions || []
      },
      pagination: {
        page: Math.floor(offset / limit) + 1,
        limit,
        total: totalSessions,
        totalPages: Math.ceil(totalSessions / limit)
      },
      filters: {
        startDate,
        endDate,
        minDuration,
        maxDuration,
        sortBy,
        sortOrder
      }
    };

    // Cache the result
    analyticsCache.set(cacheKey, {
      data: responseData.data,
      meta: {
        pagination: responseData.pagination,
        filters: responseData.filters,
        queryTime: new Date().toISOString(),
        cached: false
      },
      cacheTime: new Date().toISOString()
    }, CACHE_TTL.SESSION_ANALYTICS);

    return NextResponse.json({
      success: true,
      ...responseData
    });

  } catch (error) {
    logError('SessionAnalyticsAPI', 'Error in session analytics', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to fetch session analytics' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/management-portal-x7z9y2/analytics/sessions
 * Get session analytics with custom aggregations
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { aggregation, timeRange, groupBy, startDate, endDate, limit = 100 } = body;

    if (!aggregation) {
      return NextResponse.json(
        { success: false, error: 'Aggregation type is required' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabaseClient();

    // Get session data with time filtering
    let query = supabase
      .from('session_analytics')
      .select('*');

    // Apply time filtering
    if (startDate && endDate) {
      query = query.gte('session_start_time', startDate);
      query = query.lte('session_start_time', endDate);
    } else if (timeRange) {
      const { startDate, endDate } = getTimeRangeFilter(timeRange);
      query = query.gte('session_start_time', startDate.toISOString());
      query = query.lte('session_start_time', endDate.toISOString());
    }

    // Get total count first (without limit)
    const { count: totalSessionsCount, error: countError } = await query
      .select('*', { count: 'exact', head: true });

    if (countError) {
      logError('SessionAnalyticsAPI', 'Error counting session data', { error: countError });
      return NextResponse.json(
        { success: false, error: 'Failed to count session data' },
        { status: 500 }
      );
    }

    // Get limited session data for detailed calculations
    const { data: sessions, error } = await query.limit(limit);

    if (error) {
      logError('SessionAnalyticsAPI', 'Error fetching session data', { error });
      return NextResponse.json(
        { success: false, error: 'Failed to fetch session data' },
        { status: 500 }
      );
    }

    // Use actual total count, not just fetched sessions
    const totalSessions = totalSessionsCount || 0;
    const totalEmailsGenerated = sessions?.reduce((sum, s) => sum + (s.emails_generated_count || 0), 0) || 0;
    const totalEmailsReceived = sessions?.reduce((sum, s) => sum + (s.emails_received_count || 0), 0) || 0;
    const totalEmailsViewed = sessions?.reduce((sum, s) => sum + (s.emails_viewed_count || 0), 0) || 0;
    const totalEmailsDeleted = sessions?.reduce((sum, s) => sum + (s.emails_deleted_count || 0), 0) || 0;
    const totalCopyActions = sessions?.reduce((sum, s) => sum + (s.copy_actions_count || 0), 0) || 0;
    const totalManualRefreshes = sessions?.reduce((sum, s) => sum + (s.manual_refresh_count || 0), 0) || 0;

    // Calculate average session duration (including active sessions)
    const sessionDurations = sessions?.map(session => {
      // Use stored duration if available, otherwise calculate for active sessions
      if (session.session_duration) {
        return session.session_duration;
      } else if (session.session_start_time) {
        const startTime = new Date(session.session_start_time);
        const endTime = session.session_end_time ? new Date(session.session_end_time) : new Date();
        return Math.floor((endTime.getTime() - startTime.getTime()) / 1000);
      }
      return 0;
    }).filter(duration => duration > 0) || [];

    const avgSessionDuration = sessionDurations.length > 0
      ? sessionDurations.reduce((sum, duration) => sum + duration, 0) / sessionDurations.length
      : 0;

    // Calculate engagement metrics
    const avgEmailsGeneratedPerSession = totalSessions > 0 ? totalEmailsGenerated / totalSessions : 0;
    const avgEmailsReceivedPerSession = totalSessions > 0 ? totalEmailsReceived / totalSessions : 0;
    const avgEmailsViewedPerSession = totalSessions > 0 ? totalEmailsViewed / totalSessions : 0;
    const avgEmailsDeletedPerSession = totalSessions > 0 ? totalEmailsDeleted / totalSessions : 0;
    const avgCopyActionsPerSession = totalSessions > 0 ? totalCopyActions / totalSessions : 0;
    const avgManualRefreshesPerSession = totalSessions > 0 ? totalManualRefreshes / totalSessions : 0;

    const emailViewRate = totalEmailsReceived > 0 ? (totalEmailsViewed / totalEmailsReceived) * 100 : 0;
    const emailDeleteRate = totalEmailsReceived > 0 ? (totalEmailsDeleted / totalEmailsReceived) * 100 : 0;

    // Calculate breakdowns
    const deviceBreakdown: Record<string, number> = {};
    const browserBreakdown: Record<string, number> = {};
    const countryBreakdown: Record<string, number> = {};
    const durationBreakdown: Record<string, number> = {
      '0-30s': 0,
      '30s-2m': 0,
      '2m-5m': 0,
      '5m-15m': 0,
      '15m+': 0
    };

    sessions?.forEach(session => {
      // Device breakdown
      const device = session.device_type || 'unknown';
      deviceBreakdown[device] = (deviceBreakdown[device] || 0) + 1;

      // Browser breakdown
      const browser = session.browser || 'unknown';
      browserBreakdown[browser] = (browserBreakdown[browser] || 0) + 1;

      // Country breakdown
      const country = session.country || 'unknown';
      countryBreakdown[country] = (countryBreakdown[country] || 0) + 1;

      // Duration breakdown (calculate for active sessions)
      let duration = session.session_duration || 0;
      if (!duration && session.session_start_time) {
        const startTime = new Date(session.session_start_time);
        const endTime = session.session_end_time ? new Date(session.session_end_time) : new Date();
        duration = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);
      }

      if (duration <= 30) {
        durationBreakdown['0-30s']++;
      } else if (duration <= 120) {
        durationBreakdown['30s-2m']++;
      } else if (duration <= 300) {
        durationBreakdown['2m-5m']++;
      } else if (duration <= 900) {
        durationBreakdown['5m-15m']++;
      } else {
        durationBreakdown['15m+']++;
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalSessions,
          totalEmailsGenerated,
          totalEmailsReceived,
          totalEmailsViewed,
          totalEmailsDeleted,
          totalCopyActions,
          totalManualRefreshes,
          avgSessionDuration
        },
        engagement: {
          avgEmailsGeneratedPerSession,
          avgEmailsReceivedPerSession,
          avgEmailsViewedPerSession,
          avgEmailsDeletedPerSession,
          avgCopyActionsPerSession,
          avgManualRefreshesPerSession,
          emailViewRate,
          emailDeleteRate
        },
        breakdowns: {
          sessionDuration: durationBreakdown,
          device: deviceBreakdown,
          browser: browserBreakdown,
          country: countryBreakdown
        }
      },
      meta: {
        aggregation,
        timeRange,
        groupBy,
        queryTime: new Date().toISOString(),
        totalRecords: totalSessions
      }
    });

  } catch (error) {
    logError('SessionAnalyticsAPI', 'Error in session analytics aggregation', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to execute aggregation' },
      { status: 500 }
    );
  }
}
