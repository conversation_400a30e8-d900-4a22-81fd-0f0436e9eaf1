/**
 * Error handling utilities for Fademail
 */

/**
 * Custom API error class
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';

    // This is needed to make instanceof work correctly in TypeScript
    Object.setPrototypeOf(this, ApiError.prototype);
  }
}

/**
 * Custom database error class
 */
export class DatabaseError extends Error {
  constructor(
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'DatabaseError';

    // This is needed to make instanceof work correctly in TypeScript
    Object.setPrototypeOf(this, DatabaseError.prototype);
  }
}

/**
 * Handles API errors in a consistent way
 *
 * @param error The error to handle
 * @returns An object with a message and status code
 */
export const handleApiError = (error: unknown): { message: string, status: number } => {
  console.error('API Error:', error);

  if (error instanceof ApiError) {
    return {
      message: error.message,
      status: error.status
    };
  }

  if (error instanceof Error) {
    return {
      message: `An error occurred: ${error.message}`,
      status: 500
    };
  }

  return {
    message: 'An unknown error occurred',
    status: 500
  };
};

/**
 * Logs an error with detailed information
 *
 * @param context The context where the error occurred
 * @param message Optional custom error message
 * @param error The error to log
 */
export const logError = (context: string, message?: string | unknown, error?: unknown): void => {
  // Handle the case where message is actually the error
  if (typeof message !== 'string' && error === undefined) {
    error = message;
    message = undefined;
  }

  if (message) {
    console.error(`Error in ${context}:`, message);
  } else {
    console.error(`Error in ${context}:`, error);
  }

  if (error) {
    console.error(error);

    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);

      if ('details' in error) {
        console.error('Error details:', (error as any).details);
      }
    }
  }
};

/**
 * Creates a user-friendly error message
 *
 * @param error The error to create a message for
 * @param fallbackMessage A fallback message if the error doesn't have one
 * @returns A user-friendly error message
 */
export const createUserFriendlyErrorMessage = (
  error: unknown,
  fallbackMessage: string = 'An error occurred. Please try again.'
): string => {
  if (error instanceof ApiError) {
    return error.message;
  }

  if (error instanceof Error) {
    // Only return the error message if it's user-friendly
    // Otherwise, use the fallback message
    return isUserFriendlyMessage(error.message)
      ? error.message
      : fallbackMessage;
  }

  return fallbackMessage;
};

/**
 * Checks if an error message is user-friendly
 *
 * @param message The message to check
 * @returns Whether the message is user-friendly
 */
const isUserFriendlyMessage = (message: string): boolean => {
  // Messages containing these terms are likely not user-friendly
  const technicalTerms = [
    'undefined',
    'null',
    'NaN',
    'syntax error',
    'cannot read property',
    'is not a function',
    'is not defined',
    'unexpected token',
    'stack',
    'trace',
    'exception'
  ];

  const lowerCaseMessage = message.toLowerCase();

  return !technicalTerms.some(term => lowerCaseMessage.includes(term));
};
