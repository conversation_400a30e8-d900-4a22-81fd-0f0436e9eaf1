'use client';

import Link from 'next/link';
import Logo from './Logo';

/**
 * Footer Component
 *
 * A sleek and modern footer for the VanishPost application
 */
export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="py-10 mt-8"
            style={{
              color: 'var(--earth-brown-medium)',
              borderTop: '1px solid var(--earth-beige-secondary)',
              background: 'linear-gradient(to bottom, var(--earth-beige-light), var(--earth-beige-secondary))'
            }}>
      <div className="max-w-5xl mx-auto px-4">
        {/* Improved grid with better responsive behavior */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company info - removed duplicate copyright */}
          <div className="col-span-1 sm:col-span-2 lg:col-span-1">
            <div className="mb-4">
              <Logo />
            </div>
            <p className="text-sm mb-4" style={{ color: 'var(--earth-brown-medium)' }}>Secure, temporary email addresses that expire automatically after 15 minutes.</p>
          </div>

          {/* Combined navigation section */}
          <div className="col-span-1">
            <h3 className="font-medium mb-3 text-sm uppercase tracking-wider" style={{ color: 'var(--earth-brown-dark)' }}>Features</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/features" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Temporary Email</Link></li>
              <li><Link href="/features" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Auto-Expiring</Link></li>
              <li><Link href="/features" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>No Registration</Link></li>
              <li><Link href="/features" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Secure & Private</Link></li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="font-medium mb-3 text-sm uppercase tracking-wider" style={{ color: 'var(--earth-brown-dark)' }}>Resources</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/faq" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>FAQ</Link></li>
              <li><Link href="/privacy" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Privacy Policy</Link></li>
              <li><Link href="/terms" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Terms of Service</Link></li>
              <li><Link href="/contact" className="hover:text-[#ce601c] transition-colors duration-200 inline-block py-1" style={{ color: 'var(--earth-brown-medium)' }}>Contact Us</Link></li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="font-medium mb-3 text-sm uppercase tracking-wider" style={{ color: 'var(--earth-brown-dark)' }}>Connect</h3>
            <div className="flex justify-start sm:justify-center lg:justify-start space-x-3 mb-4">
              <a href="#" className="p-2 rounded-full shadow-sm text-[#ce601c] transition-all duration-200 transform hover:scale-105"
                 style={{ backgroundColor: 'var(--earth-beige-light)' }}
                 onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--brand-orange-hover)'}
                 onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--earth-beige-light)'}
                 aria-label="Twitter">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                </svg>
              </a>
              <a href="#" className="p-2 rounded-full shadow-sm text-[#ce601c] transition-all duration-200 transform hover:scale-105"
                 style={{ backgroundColor: 'var(--earth-beige-light)' }}
                 onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--brand-orange-hover)'}
                 onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--earth-beige-light)'}
                 aria-label="Facebook">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                </svg>
              </a>
              <a href="#" className="p-2 rounded-full shadow-sm text-[#ce601c] transition-all duration-200 transform hover:scale-105"
                 style={{ backgroundColor: 'var(--earth-beige-light)' }}
                 onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--brand-orange-hover)'}
                 onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--earth-beige-light)'}
                 aria-label="Instagram">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
                </svg>
              </a>
              <a href="#" className="p-2 rounded-full shadow-sm text-[#ce601c] transition-all duration-200 transform hover:scale-105"
                 style={{ backgroundColor: 'var(--earth-beige-light)' }}
                 onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--brand-orange-hover)'}
                 onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--earth-beige-light)'}
                 aria-label="LinkedIn">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"/>
                </svg>
              </a>
            </div>
            <p className="text-xs text-center sm:text-center lg:text-left" style={{ color: 'var(--earth-brown-medium)' }}>Stay updated with our latest features and announcements.</p>
          </div>
        </div>

        <div className="mt-10 pt-6 flex flex-col md:flex-row justify-between items-center"
             style={{ borderTop: '1px solid var(--earth-beige-secondary)' }}>
          <p className="text-xs mb-4 md:mb-0 font-medium" style={{ color: 'var(--earth-brown-medium)' }}>© {currentYear} VanishPost. All rights reserved.</p>
          <div className="flex space-x-6 text-xs">
            <Link href="/privacy" className="hover:text-[#ce601c] transition-colors font-medium" style={{ color: 'var(--earth-brown-medium)' }}>Privacy Policy</Link>
            <Link href="/terms" className="hover:text-[#ce601c] transition-colors font-medium" style={{ color: 'var(--earth-brown-medium)' }}>Terms of Service</Link>
            <Link href="/cookies" className="hover:text-[#ce601c] transition-colors font-medium" style={{ color: 'var(--earth-brown-medium)' }}>Cookie Policy</Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
