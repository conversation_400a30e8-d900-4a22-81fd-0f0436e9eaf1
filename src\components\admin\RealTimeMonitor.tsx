'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Spinner } from '../ui/Spinner';

interface LogEntry {
  id: number;
  level: string;
  category: string;
  message: string;
  timestamp: string;
  metadata?: any;
}

interface Alert {
  category: string;
  level: string;
  message: string;
  count: number;
  threshold: number;
  timestamp: string;
  sampleMessage: string;
  metadata?: any;
}

interface RealTimeMonitorProps {
  maxLogs?: number;
  level?: string;
  category?: string;
}

export default function RealTimeMonitor({ maxLogs = 100, level, category }: RealTimeMonitorProps) {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  // Function to create and set up EventSource
  const setupEventSource = useCallback(() => {
    // Close existing connection if any
    if (eventSourceRef.current) {
      console.log('Closing existing EventSource connection...');
      eventSourceRef.current.close();
      eventSourceRef.current = null;

      // Add a small delay before reconnecting to allow server cleanup
      return setTimeout(() => {
        createNewEventSource();
      }, 1000); // Wait 1 second before creating a new connection
    } else {
      return createNewEventSource();
    }
  }, [maxLogs, level, category]);

  // Function to create a new EventSource connection
  const createNewEventSource = useCallback(() => {
    // Build query parameters
    const params = new URLSearchParams();
    if (level) params.append('level', level);
    if (category) params.append('category', category);

    console.log('Creating new EventSource connection...');

    // Create EventSource for SSE (using secure admin path)
    const eventSource = new EventSource(`/api/management-portal-x7z9y2/logs/stream?${params.toString()}`);
    eventSourceRef.current = eventSource;

    // Handle connection open
    eventSource.onopen = () => {
      console.log('EventSource connection opened');
      setConnected(true);
      setError(null);
    };

    // Handle messages
    eventSource.onmessage = (event) => {
      console.log('SSE message received:', event.data);
      try {
        const data = JSON.parse(event.data);
        console.log('Parsed SSE data:', data);

        if (data.type === 'connected') {
          setConnected(true);
          console.log('Connected to log stream');
        } else if (data.type === 'log') {
          console.log('Log received:', data.log);
          setLogs((prevLogs) => {
            const newLogs = [...prevLogs, data.log];
            // Limit the number of logs
            return newLogs.slice(-maxLogs);
          });
        } else if (data.type === 'heartbeat') {
          console.log('Heartbeat received:', data.timestamp);
          // No need to update UI for heartbeats
        } else if (data.type === 'alert') {
          console.log('Alert received:', data.alert);
          setAlerts((prevAlerts) => {
            const newAlerts = [data.alert, ...prevAlerts];
            // Limit the number of alerts
            return newAlerts.slice(0, 10);
          });

          // Play sound for critical alerts
          if (data.alert.level === 'critical') {
            const audio = new Audio('/sounds/alert.mp3');
            audio.play().catch(e => console.error('Failed to play alert sound:', e));
          }
        }
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    };

    // Handle errors
    eventSource.onerror = (event) => {
      console.error('EventSource error:', event);
      setError('Connection error. Reconnecting...');
      setConnected(false);

      // Close the current connection
      eventSource.close();
      eventSourceRef.current = null;

      // Try to reconnect after a delay
      setTimeout(() => {
        console.log('Attempting to reconnect after error...');
        createNewEventSource();
      }, 2000); // Wait 2 seconds before reconnecting
    };

    return eventSource;
  }, [maxLogs, level, category]);

  // Set up EventSource on component mount and when dependencies change
  useEffect(() => {
    const result = setupEventSource();
    let timeoutId: number | undefined;

    // Store timeout ID if setupEventSource returned a timeout
    if (typeof result === 'number') {
      timeoutId = result;
    }

    // Clean up on unmount
    return () => {
      console.log('Cleaning up EventSource connection');

      // Clear timeout if it exists
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Close EventSource if it exists
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
    };
  }, [setupEventSource]);

  // Add a manual reconnect button
  const handleReconnect = useCallback(() => {
    console.log('Manual reconnect requested');
    setConnected(false);
    setError('Reconnecting...');
    setupEventSource();
  }, [setupEventSource]);

  // Add a clear logs button
  const handleClearLogs = useCallback(() => {
    console.log('Clearing logs');
    setLogs([]);
  }, []);

  // Get log level badge color
  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'critical':
        return 'bg-red-600 text-white';
      case 'error':
        return 'bg-red-500 text-white';
      case 'warning':
        return 'bg-yellow-500 text-white';
      case 'info':
        return 'bg-blue-500 text-white';
      case 'debug':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-gray-400 text-white';
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
          <h2 className="text-xl font-semibold">Real-Time Monitoring</h2>
        </div>
        <div className="flex items-center">
          {connected ? (
            <Badge className="bg-green-500 mr-2">Connected</Badge>
          ) : (
            <Badge className="bg-red-500 mr-2">Disconnected</Badge>
          )}
          {!connected && <Spinner size="sm" className="mr-2" />}
          <button
            onClick={handleReconnect}
            className="px-2 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded"
            title="Manually reconnect to the log stream"
          >
            Reconnect
          </button>
        </div>
      </div>

      {error && (
        <div className="p-2 bg-red-100 border border-red-300 rounded text-red-800">
          {error}
        </div>
      )}

      {alerts.length > 0 && (
        <Card className="p-4 bg-red-50 border-red-200">
          <h3 className="font-semibold text-lg mb-2">Active Alerts</h3>
          <div className="space-y-2">
            {alerts.map((alert, index) => (
              <div key={index} className="p-3 bg-white rounded shadow-sm border-l-4 border-red-500">
                <div className="flex justify-between">
                  <Badge className={getLevelColor(alert.level)}>{alert.level}</Badge>
                  <span className="text-sm text-gray-500">{formatTimestamp(alert.timestamp)}</span>
                </div>
                <div className="font-medium mt-1">{alert.message}</div>
                <div className="text-sm text-gray-700 mt-1">
                  Category: {alert.category} | Count: {alert.count}/{alert.threshold}
                </div>
                <div className="text-sm text-gray-600 mt-1 italic">
                  Sample: {alert.sampleMessage}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      <Card className="p-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold text-lg">Live Logs</h3>
          <button
            onClick={handleClearLogs}
            className="px-2 py-1 text-xs bg-gray-500 hover:bg-gray-600 text-white rounded flex items-center"
            title="Clear all logs from the display"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Clear Logs
          </button>
        </div>
        <div className="bg-gray-900 text-gray-100 p-4 rounded font-mono text-sm h-96 overflow-y-auto">
          {logs.length === 0 ? (
            <div className="text-gray-500 italic">Waiting for logs...</div>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="mb-1 border-b border-gray-800 pb-1">
                <span className={`inline-block px-2 py-0.5 rounded text-xs mr-2 ${getLevelColor(log.level)}`}>
                  {log.level}
                </span>
                <span className="text-gray-400 mr-2">[{formatTimestamp(log.timestamp)}]</span>
                <span className="text-yellow-300 mr-2">{log.category}:</span>
                <span>{log.message}</span>
                {log.metadata && (
                  <div className="text-gray-400 text-xs mt-1 pl-4">
                    {JSON.stringify(log.metadata)}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </Card>
    </div>
  );
}
