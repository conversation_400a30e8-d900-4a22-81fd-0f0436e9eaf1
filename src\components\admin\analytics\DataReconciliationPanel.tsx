'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, RefreshCw, Settings, Zap } from 'lucide-react';

interface ReconciliationResult {
  success: boolean;
  report?: {
    discrepanciesFound: number;
    autoFixedCount: number;
    manualFixRequired: number;
    summary: {
      emailGenerationGap: number;
      sessionAggregationErrors: number;
      cacheInconsistencies: number;
    };
  };
  message?: string;
  error?: string;
}

export default function DataReconciliationPanel() {
  const [isRunning, setIsRunning] = useState(false);
  const [lastResult, setLastResult] = useState<ReconciliationResult | null>(null);
  const [lastRun, setLastRun] = useState<Date | null>(null);

  /**
   * Run quick health check
   */
  const runQuickCheck = async () => {
    setIsRunning(true);
    try {
      const response = await fetch('/api/management-portal-x7z9y2/analytics/data-reconciliation?quickCheck=true');
      const result = await response.json();
      
      setLastResult(result);
      setLastRun(new Date());
    } catch (error) {
      setLastResult({
        success: false,
        error: error instanceof Error ? error.message : 'Quick check failed'
      });
    } finally {
      setIsRunning(false);
    }
  };

  /**
   * Run session reconciliation
   */
  const runSessionReconciliation = async () => {
    setIsRunning(true);
    try {
      const response = await fetch('/api/management-portal-x7z9y2/analytics/reconcile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reconcileAll: true })
      });
      
      const result = await response.json();
      setLastResult(result);
      setLastRun(new Date());
    } catch (error) {
      setLastResult({
        success: false,
        error: error instanceof Error ? error.message : 'Session reconciliation failed'
      });
    } finally {
      setIsRunning(false);
    }
  };

  /**
   * Run full reconciliation with auto-fix
   */
  const runFullReconciliation = async () => {
    setIsRunning(true);
    try {
      const response = await fetch('/api/management-portal-x7z9y2/analytics/data-reconciliation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          autoFix: true,
          includeCache: true
        })
      });
      
      const result = await response.json();
      setLastResult(result);
      setLastRun(new Date());
    } catch (error) {
      setLastResult({
        success: false,
        error: error instanceof Error ? error.message : 'Full reconciliation failed'
      });
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Data Reconciliation
        </CardTitle>
        <p className="text-sm text-gray-600">
          Detect and fix analytics data discrepancies
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Action Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <Button
            onClick={runQuickCheck}
            disabled={isRunning}
            variant="outline"
            className="flex items-center gap-2"
          >
            <CheckCircle className="h-4 w-4" />
            {isRunning ? 'Checking...' : 'Quick Check'}
          </Button>

          <Button
            onClick={runSessionReconciliation}
            disabled={isRunning}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            {isRunning ? 'Fixing...' : 'Fix Sessions'}
          </Button>

          <Button
            onClick={runFullReconciliation}
            disabled={isRunning}
            variant="primary"
            className="flex items-center gap-2"
          >
            <Zap className="h-4 w-4" />
            {isRunning ? 'Running...' : 'Full Fix'}
          </Button>
        </div>

        {/* Last Run Info */}
        {lastRun && (
          <div className="text-xs text-gray-500">
            Last run: {lastRun.toLocaleString()}
          </div>
        )}

        {/* Results */}
        {lastResult && (
          <div className="space-y-3">
            {lastResult.success ? (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-700">
                    Reconciliation Completed
                  </span>
                </div>

                {lastResult.report && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="text-lg font-bold text-blue-700">
                        {lastResult.report.discrepanciesFound}
                      </div>
                      <div className="text-xs text-blue-600">Issues Found</div>
                    </div>

                    <div className="text-center p-2 bg-green-50 rounded">
                      <div className="text-lg font-bold text-green-700">
                        {lastResult.report.autoFixedCount}
                      </div>
                      <div className="text-xs text-green-600">Auto-Fixed</div>
                    </div>

                    <div className="text-center p-2 bg-orange-50 rounded">
                      <div className="text-lg font-bold text-orange-700">
                        {lastResult.report.manualFixRequired}
                      </div>
                      <div className="text-xs text-orange-600">Manual Fix</div>
                    </div>

                    <div className="text-center p-2 bg-purple-50 rounded">
                      <div className="text-lg font-bold text-purple-700">
                        {lastResult.report.summary.emailGenerationGap}
                      </div>
                      <div className="text-xs text-purple-600">Email Gaps</div>
                    </div>
                  </div>
                )}

                {lastResult.message && (
                  <div className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                    {lastResult.message}
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium text-red-700">
                    Reconciliation Failed
                  </span>
                </div>
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {lastResult.error}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Help Text */}
        <div className="text-xs text-gray-500 space-y-1">
          <div><strong>Quick Check:</strong> Identifies discrepancies without fixing</div>
          <div><strong>Fix Sessions:</strong> Corrects session aggregation issues</div>
          <div><strong>Full Fix:</strong> Comprehensive reconciliation with auto-repair</div>
        </div>
      </CardContent>
    </Card>
  );
}
