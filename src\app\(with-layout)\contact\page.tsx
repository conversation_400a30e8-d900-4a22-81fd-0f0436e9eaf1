import React from 'react';
import type { Metadata } from 'next';
import ContactForm from '@/components/ContactForm';

export const metadata: Metadata = {
  title: 'Contact Us | VanishPost',
  description: 'Get in touch with the VanishPost team for support, feedback, or partnership inquiries.',
};

export default function ContactPage() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-12 sm:py-16">
      <div className="text-center mb-12">
        <h1 className="text-3xl sm:text-4xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Contact Us</h1>
        <p className="text-lg max-w-3xl mx-auto" style={{ color: 'var(--earth-brown-medium)' }}>
          Have questions, feedback, or need assistance? We're here to help!
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 mb-12">
        {/* Contact Form */}
        <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
          <h2 className="text-xl font-semibold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Send Us a Message</h2>
          <ContactForm />
        </div>

        {/* Contact Information */}
        <div>
          <div className="p-6 rounded-md shadow-sm mb-6" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
            <h2 className="text-xl font-semibold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Contact Information</h2>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="p-2 rounded-full mr-3 mt-1" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-sm font-medium" style={{ color: 'var(--earth-brown-dark)' }}>Email</h3>
                  <p style={{ color: 'var(--earth-brown-medium)' }}><EMAIL></p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="p-2 rounded-full mr-3 mt-1" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ color: 'var(--earth-brown-medium)' }}>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-sm font-medium" style={{ color: 'var(--earth-brown-dark)' }}>Response Time</h3>
                  <p style={{ color: 'var(--earth-brown-medium)' }}>We typically respond within 24-48 hours during business days.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="p-6 rounded-md shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
            <h2 className="text-xl font-semibold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Connect With Us</h2>
            <p className="mb-4" style={{ color: 'var(--earth-brown-medium)' }}>
              Follow us on social media for updates, privacy tips, and more.
            </p>
            <div className="flex space-x-4">
              <a href="https://twitter.com/vanishpost" className="p-3 rounded-full transition-colors social-icon"
                 style={{ backgroundColor: 'var(--earth-beige-secondary)', color: 'var(--earth-brown-medium)' }}
                 aria-label="Twitter">
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </a>
              <a href="https://facebook.com/vanishpost" className="p-3 rounded-full transition-colors social-icon"
                 style={{ backgroundColor: 'var(--earth-beige-secondary)', color: 'var(--earth-brown-medium)' }}
                 aria-label="Facebook">
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                </svg>
              </a>
              <a href="https://instagram.com/vanishpost" className="p-3 rounded-full transition-colors social-icon"
                 style={{ backgroundColor: 'var(--earth-beige-secondary)', color: 'var(--earth-brown-medium)' }}
                 aria-label="Instagram">
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z" />
                </svg>
              </a>
              <a href="https://linkedin.com/company/vanishpost" className="p-3 rounded-full transition-colors social-icon"
                 style={{ backgroundColor: 'var(--earth-beige-secondary)', color: 'var(--earth-brown-medium)' }}
                 aria-label="LinkedIn">
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="p-8 rounded-md text-center" style={{ backgroundColor: 'var(--earth-beige-secondary)' }}>
        <h2 className="text-2xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Frequently Asked Questions</h2>
        <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
          Find quick answers to common questions about VanishPost.
        </p>
        <a href="/faq" className="inline-block bg-[#ce601c] text-white px-6 py-3 rounded-md font-medium hover:bg-[#b85518] transition-colors">
          Visit FAQ Page
        </a>
      </div>
    </div>
  );
}
