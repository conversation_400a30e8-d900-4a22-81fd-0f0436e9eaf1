#!/usr/bin/env node

/**
 * Analytics Data Reconciliation Script
 * 
 * This script runs comprehensive analytics data reconciliation
 * to fix discrepancies between different data sources.
 * 
 * Usage:
 *   node scripts/run-analytics-reconciliation.js [options]
 * 
 * Options:
 *   --auto-fix          Automatically fix detected issues
 *   --date-range        Specify date range (format: YYYY-MM-DD,YYYY-MM-DD)
 *   --sessions-only     Only reconcile session data
 *   --quick-check       Run quick health check only
 *   --verbose           Show detailed output
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  autoFix: args.includes('--auto-fix'),
  sessionsOnly: args.includes('--sessions-only'),
  quickCheck: args.includes('--quick-check'),
  verbose: args.includes('--verbose'),
  dateRange: null
};

// Parse date range
const dateRangeIndex = args.indexOf('--date-range');
if (dateRangeIndex !== -1 && args[dateRangeIndex + 1]) {
  const [start, end] = args[dateRangeIndex + 1].split(',');
  if (start && end) {
    options.dateRange = {
      start: `${start}T00:00:00.000Z`,
      end: `${end}T23:59:59.999Z`
    };
  }
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

/**
 * Main reconciliation function
 */
async function runReconciliation() {
  console.log('🔧 VanishPost Analytics Data Reconciliation');
  console.log('==========================================');
  console.log(`Options:`, options);
  console.log('');

  try {
    if (options.quickCheck) {
      await runQuickCheck();
    } else if (options.sessionsOnly) {
      await runSessionReconciliation();
    } else {
      await runFullReconciliation();
    }

    console.log('\n✅ Reconciliation completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('\n❌ Reconciliation failed:', error.message);
    if (options.verbose) {
      console.error('Full error:', error);
    }
    process.exit(1);
  }
}

/**
 * Run quick health check
 */
async function runQuickCheck() {
  console.log('🔍 Running quick health check...');

  try {
    const response = await fetch(`${BASE_URL}/api/management-portal-x7z9y2/analytics/data-reconciliation?quickCheck=true`);
    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Quick check failed');
    }

    console.log('\n📊 Health Check Results:');
    console.log(`   Discrepancies Found: ${result.summary.discrepanciesFound}`);
    console.log(`   Critical Issues: ${result.summary.criticalIssues}`);
    console.log(`   High Priority Issues: ${result.summary.highPriorityIssues}`);
    console.log(`   Last Checked: ${result.summary.lastChecked}`);

    if (result.recommendations && result.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      result.recommendations.forEach(rec => {
        console.log(`   • ${rec}`);
      });
    }

  } catch (error) {
    throw new Error(`Quick check failed: ${error.message}`);
  }
}

/**
 * Run session-only reconciliation
 */
async function runSessionReconciliation() {
  console.log('🔄 Running session reconciliation...');

  try {
    const payload = {
      reconcileAll: true
    };

    const response = await fetch(`${BASE_URL}/api/management-portal-x7z9y2/analytics/reconcile`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Session reconciliation failed');
    }

    console.log('\n📊 Session Reconciliation Results:');
    console.log(`   Sessions Reconciled: ${result.results.reconciledSessions}`);
    console.log(`   Failed Sessions: ${result.results.failedSessions}`);
    console.log(`   Total Processed: ${result.results.totalProcessed}`);

  } catch (error) {
    throw new Error(`Session reconciliation failed: ${error.message}`);
  }
}

/**
 * Run full comprehensive reconciliation
 */
async function runFullReconciliation() {
  console.log('🔄 Running comprehensive data reconciliation...');

  try {
    const payload = {
      autoFix: options.autoFix,
      includeCache: true
    };

    if (options.dateRange) {
      payload.dateRange = options.dateRange;
      console.log(`   Date Range: ${options.dateRange.start} to ${options.dateRange.end}`);
    }

    console.log(`   Auto-Fix: ${options.autoFix ? 'Enabled' : 'Disabled'}`);

    const response = await fetch(`${BASE_URL}/api/management-portal-x7z9y2/analytics/data-reconciliation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Full reconciliation failed');
    }

    const report = result.report;

    console.log('\n📊 Comprehensive Reconciliation Results:');
    console.log(`   Total Checks: ${report.totalChecks}`);
    console.log(`   Discrepancies Found: ${report.discrepanciesFound}`);
    console.log(`   Auto-Fixed Issues: ${report.autoFixedCount}`);
    console.log(`   Manual Fix Required: ${report.manualFixRequired}`);

    console.log('\n📈 Summary by Category:');
    console.log(`   Email Generation Gaps: ${report.summary.emailGenerationGap}`);
    console.log(`   Session Aggregation Errors: ${report.summary.sessionAggregationErrors}`);
    console.log(`   Cache Inconsistencies: ${report.summary.cacheInconsistencies}`);

    if (options.verbose && report.discrepancies.length > 0) {
      console.log('\n🔍 Detailed Discrepancies:');
      report.discrepancies.forEach((disc, index) => {
        console.log(`   ${index + 1}. [${disc.severity.toUpperCase()}] ${disc.description}`);
        console.log(`      Actual: ${disc.actualValue}, Expected: ${disc.expectedValue}`);
        console.log(`      Difference: ${disc.difference} (${disc.percentageDiff.toFixed(1)}%)`);
        console.log('');
      });
    }

  } catch (error) {
    throw new Error(`Full reconciliation failed: ${error.message}`);
  }
}

/**
 * Show usage information
 */
function showUsage() {
  console.log(`
Usage: node scripts/run-analytics-reconciliation.js [options]

Options:
  --auto-fix              Automatically fix detected issues
  --date-range START,END  Specify date range (format: YYYY-MM-DD,YYYY-MM-DD)
  --sessions-only         Only reconcile session data
  --quick-check           Run quick health check only
  --verbose               Show detailed output
  --help                  Show this help message

Examples:
  # Quick health check
  node scripts/run-analytics-reconciliation.js --quick-check

  # Fix all session data
  node scripts/run-analytics-reconciliation.js --sessions-only

  # Full reconciliation with auto-fix for specific date
  node scripts/run-analytics-reconciliation.js --auto-fix --date-range 2025-07-11,2025-07-11

  # Comprehensive reconciliation with detailed output
  node scripts/run-analytics-reconciliation.js --auto-fix --verbose
`);
}

// Handle help flag
if (args.includes('--help') || args.includes('-h')) {
  showUsage();
  process.exit(0);
}

// Validate environment variables
if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   NEXT_PUBLIC_SUPABASE_URL');
  console.error('   SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Run the reconciliation
runReconciliation();
