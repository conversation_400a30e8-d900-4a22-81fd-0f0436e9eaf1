/**
 * Data Reconciliation API Endpoint
 * 
 * This endpoint provides comprehensive data reconciliation capabilities
 * to detect and fix analytics data discrepancies.
 */

import { NextRequest, NextResponse } from 'next/server';
import { runDataReconciliation } from '@/lib/analytics/dataReconciliation';
import { logError, logInfo } from '@/lib/logging';

/**
 * POST /api/management-portal-x7z9y2/analytics/data-reconciliation
 * Run comprehensive data reconciliation
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      autoFix = false, 
      dateRange, 
      includeCache = true 
    } = body;

    logInfo('DataReconciliationAPI', 'Starting data reconciliation', { 
      autoFix, 
      dateRange, 
      includeCache 
    });

    // Run the reconciliation
    const report = await runDataReconciliation({
      autoFix,
      dateRange,
      includeCache
    });

    // Log summary
    logInfo('DataReconciliationAPI', 'Data reconciliation completed', {
      discrepanciesFound: report.discrepanciesFound,
      autoFixedCount: report.autoFixedCount,
      summary: report.summary
    });

    return NextResponse.json({
      success: true,
      report,
      message: `Reconciliation completed. Found ${report.discrepanciesFound} discrepancies, auto-fixed ${report.autoFixedCount} issues.`
    });

  } catch (error) {
    logError('DataReconciliationAPI', 'Error during data reconciliation', { error });
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to run data reconciliation',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/management-portal-x7z9y2/analytics/data-reconciliation
 * Get reconciliation status and recent reports
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const quickCheck = searchParams.get('quickCheck') === 'true';

    if (quickCheck) {
      // Run a quick reconciliation check without auto-fix
      const report = await runDataReconciliation({
        autoFix: false,
        includeCache: false
      });

      return NextResponse.json({
        success: true,
        quickCheck: true,
        summary: {
          discrepanciesFound: report.discrepanciesFound,
          criticalIssues: report.discrepancies.filter(d => d.severity === 'critical').length,
          highPriorityIssues: report.discrepancies.filter(d => d.severity === 'high').length,
          lastChecked: report.timestamp
        },
        recommendations: generateRecommendations(report)
      });
    }

    // Return general information about the reconciliation system
    return NextResponse.json({
      success: true,
      service: 'Data Reconciliation System',
      version: '1.0.0',
      capabilities: {
        emailGenerationTracking: 'Compares temp_emails table with analytics events',
        sessionAggregation: 'Validates session metrics against actual events',
        autoFix: 'Automatically corrects session aggregation discrepancies',
        cacheConsistency: 'Checks cached data against database (future feature)'
      },
      endpoints: {
        'POST /': 'Run comprehensive reconciliation',
        'GET /?quickCheck=true': 'Quick discrepancy check',
        'GET /': 'Service information'
      },
      usage: {
        quickCheck: 'Use for dashboard health indicators',
        fullReconciliation: 'Use for periodic data maintenance',
        autoFix: 'Use to automatically correct known issues'
      }
    });

  } catch (error) {
    logError('DataReconciliationAPI', 'Error getting reconciliation status', { error });
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get reconciliation status',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * Generate recommendations based on reconciliation report
 */
function generateRecommendations(report: any): string[] {
  const recommendations: string[] = [];

  // Check for email generation gaps
  const emailGaps = report.discrepancies.filter((d: any) => d.type === 'email_generation');
  if (emailGaps.length > 0) {
    const criticalGaps = emailGaps.filter((d: any) => d.severity === 'critical');
    if (criticalGaps.length > 0) {
      recommendations.push('CRITICAL: Large email generation tracking gap detected. Check client-side analytics initialization.');
    } else {
      recommendations.push('Email generation tracking has minor gaps. Consider adding server-side backup tracking.');
    }
  }

  // Check for session aggregation issues
  const sessionIssues = report.discrepancies.filter((d: any) => d.type === 'session_aggregation');
  if (sessionIssues.length > 0) {
    recommendations.push(`Session aggregation discrepancies found in ${sessionIssues.length} sessions. Run auto-fix to correct.`);
  }

  // Check overall health
  if (report.discrepanciesFound === 0) {
    recommendations.push('✅ All analytics data is consistent and accurate.');
  } else if (report.discrepanciesFound > 10) {
    recommendations.push('⚠️ Multiple data discrepancies detected. Schedule regular reconciliation runs.');
  }

  // Performance recommendations
  if (report.summary.emailGenerationGap > 5) {
    recommendations.push('Consider implementing real-time analytics validation to prevent future discrepancies.');
  }

  return recommendations;
}
