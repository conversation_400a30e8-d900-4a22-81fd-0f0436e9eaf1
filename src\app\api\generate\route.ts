import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { createTempEmail, getTempEmailByAddress } from '@/lib/supabase-db';
import { EmailGenerationResponse } from '@/lib/types';
import { TIMEOUTS } from '@/lib/constants';
import { ApiError, handleApiError, logError } from '@/lib/errorHandling';
import { getDomainForEmailGeneration } from '@/lib/config/domainService';
import { getConfig } from '@/lib/config/configService';
import { logger } from '@/lib/logging/Logger';
import { TempEmail } from '@/lib/types';
import { storeAnalyticsEvent } from '@/lib/analytics/emailGeneratorAnalytics';
import { updateSessionMetrics } from '@/lib/analytics/sessionManager';

// List of common first names for generating human-like email addresses
const firstNames = [
  'john', 'jane', 'michael', 'sarah', 'david', 'emma', 'james', 'olivia',
  'robert', 'sophia', 'william', 'ava', 'joseph', 'mia', 'thomas', 'emily',
  'charles', 'amelia', 'daniel', 'ella', 'matthew', 'grace', 'anthony', 'chloe',
  'mark', 'lily', 'donald', 'hannah', 'steven', 'zoe', 'paul', 'samantha',
  'andrew', 'natalie', 'joshua', 'addison', 'kenneth', 'audrey', 'kevin', 'victoria',
  'brian', 'brooklyn', 'george', 'claire', 'timothy', 'skylar', 'ronald', 'lucy',
  'jason', 'anna', 'ryan', 'caroline', 'jacob', 'genesis', 'gary', 'kennedy',
  'nicholas', 'allison', 'eric', 'gabriella', 'jonathan', 'maya', 'stephen', 'aaliyah'
];

/**
 * Generate a human-like email address with a random string
 */
async function generateEmailAddress(): Promise<string> {
  // Select a random first name
  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];

  // Generate a random 4-6 digit number (between 1000 and 999999)
  // This provides enough uniqueness while keeping addresses shorter
  const digitLength = Math.floor(Math.random() * 3) + 4; // Random length between 4-6 digits
  const maxValue = Math.pow(10, digitLength);
  const minValue = Math.pow(10, digitLength - 1);
  const randomDigits = Math.floor(minValue + Math.random() * (maxValue - minValue)).toString();

  // Get domain using the domain rotation logic
  const domain = await getDomainForEmailGeneration();

  await logger.info('EMAIL_GENERATION', `Generating email with domain: ${domain}`);

  return `${firstName}${randomDigits}@${domain}`;
}

/**
 * Generate a temporary email address
 *
 * @route POST /api/generate
 */
/**
 * Generate a unique email address with retry logic for collisions
 *
 * @param maxRetries Maximum number of retries if collision occurs
 * @returns A unique email address and its expiration date
 */
async function generateUniqueEmailAddress(maxRetries: number = 3): Promise<{ emailAddress: string, expirationDate: Date }> {
  // Get email expiration time from configuration
  let expirationMinutes;
  try {
    expirationMinutes = await getConfig('emailExpirationMinutes');
  } catch (configError) {
    // Fallback to default value if configuration is not found
    await logger.error('EMAIL_CONFIG', 'Error getting email expiration time from configuration, using default', { configError });
    expirationMinutes = 30; // Default: 30 minutes
  }

  // Log the expiration time for debugging
  await logger.info('EMAIL_CONFIG', `Using email expiration time: ${expirationMinutes} minutes`);

  // Set expiration date based on configuration
  const expirationDate = new Date();
  expirationDate.setMinutes(expirationDate.getMinutes() + expirationMinutes);

  // Try to generate a unique email address with retry logic
  let attempts = 0;
  let emailAddress: string;
  let tempEmail: TempEmail | null = null;

  while (attempts < maxRetries) {
    attempts++;
    emailAddress = await generateEmailAddress();

    try {
      // Check if this email address already exists
      const existingEmail = await getTempEmailByAddress(emailAddress);

      if (!existingEmail) {
        // Email address is unique, store it in the database
        tempEmail = await createTempEmail(emailAddress, expirationDate);
        await logger.info('EMAIL_GENERATION', `Generated unique email ${emailAddress} with expiration in ${expirationMinutes} minutes (attempt ${attempts})`);
        break;
      } else {
        await logger.info('EMAIL_GENERATION', `Collision detected for ${emailAddress}, retrying... (attempt ${attempts}/${maxRetries})`);
      }
    } catch (error) {
      // If there's a unique constraint violation, retry
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('unique constraint') || errorMessage.includes('duplicate key')) {
        await logger.info('EMAIL_GENERATION', `Database collision for ${emailAddress}, retrying... (attempt ${attempts}/${maxRetries})`);
      } else {
        // For other errors, rethrow
        throw error;
      }
    }
  }

  // If we couldn't generate a unique email after max retries, throw an error
  if (!tempEmail) {
    throw new Error(`Failed to generate a unique email address after ${maxRetries} attempts`);
  }

  return {
    emailAddress: tempEmail.emailAddress,
    expirationDate: tempEmail.expirationDate
  };
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Generate a unique email address with retry logic
    const { emailAddress, expirationDate } = await generateUniqueEmailAddress();

    // Track analytics for email generation (server-side backup)
    try {
      // Get session ID from request headers or body
      const sessionId = request.headers.get('x-session-id') ||
                       request.headers.get('session-id') ||
                       `server_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Get user agent for device/browser detection
      const userAgent = request.headers.get('user-agent') || '';
      const deviceType = /Mobile|Android|iPhone|iPad/i.test(userAgent) ? 'mobile' : 'desktop';
      const browser = userAgent.includes('Chrome') ? 'Chrome' :
                     userAgent.includes('Firefox') ? 'Firefox' :
                     userAgent.includes('Safari') ? 'Safari' : 'Unknown';

      // Store analytics event (server-side tracking as backup)
      await storeAnalyticsEvent({
        sessionId,
        eventType: 'email_address_generated',
        pagePath: '/',
        browser,
        deviceType,
        country: 'unknown',
        additionalData: {
          emailAddress: emailAddress,
          domain: emailAddress.split('@')[1] || 'unknown',
          source: 'server_api',
          timestamp: new Date().toISOString()
        },
      });

      // Update session metrics if session exists
      await updateSessionMetrics(sessionId, {
        emailsGeneratedCount: 1,
      }).catch(err => {
        // Don't fail the request if session update fails
        console.warn('Failed to update session metrics:', err);
      });

      await logger.info('EMAIL_GENERATION', `Analytics tracked for email generation: ${emailAddress}`, { sessionId });
    } catch (analyticsError) {
      // Don't fail the email generation if analytics fails
      await logger.warn('EMAIL_GENERATION', `Analytics tracking failed for ${emailAddress}`, { error: analyticsError });
    }

    // Return the generated email address and expiration date
    const response: EmailGenerationResponse = {
      emailAddress: emailAddress,
      expirationDate: expirationDate,
      success: true
    };

    return NextResponse.json(response);
  } catch (error) {
    await logger.error('EMAIL_GENERATION', `Failed to generate email address: ${error instanceof Error ? error.message : String(error)}`, { error });

    const { message, status } = handleApiError(error);

    const errorResponse: EmailGenerationResponse = {
      emailAddress: '',
      expirationDate: new Date(),
      success: false,
      message: message || 'Failed to generate email address'
    };

    return NextResponse.json(errorResponse, { status });
  }
}

/**
 * Check if an email address exists and is valid
 *
 * @route GET /api/generate?email=<EMAIL>
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Get the email address from the query parameters
    const { searchParams } = new URL(request.url);
    const emailAddress = searchParams.get('email');

    if (!emailAddress) {
      return NextResponse.json({
        success: false,
        message: 'Email address is required'
      }, { status: 400 });
    }

    // Check if the email address exists in the database
    const tempEmail = await getTempEmailByAddress(emailAddress);

    if (!tempEmail) {
      return NextResponse.json({
        success: false,
        message: 'Email address not found'
      }, { status: 404 });
    }

    // Check if the email address has expired
    const now = new Date();
    if (tempEmail.expirationDate < now) {
      return NextResponse.json({
        success: false,
        message: 'Email address has expired'
      }, { status: 410 });
    }

    // Return the email address and expiration date
    const response: EmailGenerationResponse = {
      emailAddress: tempEmail.emailAddress,
      expirationDate: tempEmail.expirationDate,
      success: true
    };

    return NextResponse.json(response);
  } catch (error) {
    await logger.error('EMAIL_VALIDATION', `Failed to check email address: ${error instanceof Error ? error.message : String(error)}`, { error });

    const { message, status } = handleApiError(error);

    return NextResponse.json({
      success: false,
      message: message || 'Failed to check email address'
    }, { status });
  }
}
