/**
 * Performance Optimization Configuration
 * 
 * Centralized configuration for performance optimizations across the
 * email authentication tools including caching, rate limiting, and resource management.
 */

// Cache configuration
export const CACHE_CONFIG = {
  // DNS validation results cache (in seconds)
  DNS_VALIDATION_TTL: 300, // 5 minutes
  
  // Generated records cache (in seconds)
  GENERATED_RECORDS_TTL: 3600, // 1 hour
  
  // Public key validation cache (in seconds)
  PUBLIC_KEY_VALIDATION_TTL: 1800, // 30 minutes
  
  // Maximum cache size (number of entries)
  MAX_CACHE_SIZE: 1000,
  
  // Cache cleanup interval (in seconds)
  CLEANUP_INTERVAL: 600 // 10 minutes
};

// Rate limiting configuration
export const RATE_LIMIT_CONFIG = {
  // DKIM generation limits
  DKIM_GENERATION: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10, // 10 requests per window per IP
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  },
  
  // DMARC generation limits
  DMARC_GENERATION: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 20, // 20 requests per window per IP
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  },
  
  // DNS validation limits
  DNS_VALIDATION: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 30, // 30 requests per window per IP
    skipSuccessfulRequests: true,
    skipFailedRequests: false
  },
  
  // Global API limits
  GLOBAL: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // 100 requests per window per IP
    skipSuccessfulRequests: false,
    skipFailedRequests: false
  }
};

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  // Maximum acceptable response times (in milliseconds)
  DKIM_GENERATION_MAX_TIME: 10000, // 10 seconds
  DMARC_GENERATION_MAX_TIME: 2000, // 2 seconds
  DNS_VALIDATION_MAX_TIME: 5000, // 5 seconds
  
  // Memory usage thresholds (in MB)
  MEMORY_WARNING_THRESHOLD: 256,
  MEMORY_CRITICAL_THRESHOLD: 512,
  
  // Error rate thresholds (percentage)
  ERROR_RATE_WARNING: 5,
  ERROR_RATE_CRITICAL: 10,
  
  // Database query thresholds (in milliseconds)
  DB_QUERY_WARNING: 1000,
  DB_QUERY_CRITICAL: 3000
};

// Resource management configuration
export const RESOURCE_CONFIG = {
  // Maximum concurrent key generations
  MAX_CONCURRENT_KEY_GENERATIONS: 5,
  
  // Maximum concurrent DNS validations
  MAX_CONCURRENT_DNS_VALIDATIONS: 10,
  
  // Key generation queue size
  KEY_GENERATION_QUEUE_SIZE: 20,
  
  // DNS validation timeout (in milliseconds)
  DNS_VALIDATION_TIMEOUT: 10000,
  
  // Database connection pool size
  DB_POOL_SIZE: 10,
  
  // Maximum request body size (in bytes)
  MAX_REQUEST_SIZE: 1024 * 1024, // 1MB
  
  // Session cleanup interval (in seconds)
  SESSION_CLEANUP_INTERVAL: 3600 // 1 hour
};

// Optimization flags
export const OPTIMIZATION_FLAGS = {
  // Enable DNS result caching
  ENABLE_DNS_CACHING: true,
  
  // Enable key generation caching (for testing)
  ENABLE_KEY_CACHING: false,
  
  // Enable request compression
  ENABLE_COMPRESSION: true,
  
  // Enable response streaming for large payloads
  ENABLE_STREAMING: true,
  
  // Enable background cleanup tasks
  ENABLE_BACKGROUND_CLEANUP: true,
  
  // Enable performance monitoring
  ENABLE_PERFORMANCE_MONITORING: true,
  
  // Enable detailed logging
  ENABLE_DETAILED_LOGGING: process.env.NODE_ENV === 'development',
  
  // Enable metrics collection
  ENABLE_METRICS_COLLECTION: true
};

// Security configuration
export const SECURITY_CONFIG = {
  // Private key encryption settings
  ENCRYPTION: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16,
    tagLength: 16
  },
  
  // Session security
  SESSION: {
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'strict' as const
  },
  
  // Input validation
  VALIDATION: {
    maxDomainLength: 253,
    maxSelectorLength: 63,
    maxEmailLength: 320,
    allowedDomainChars: /^[a-zA-Z0-9.-]+$/,
    allowedSelectorChars: /^[a-zA-Z0-9._-]+$/
  },
  
  // CORS settings
  CORS: {
    origin: process.env.NODE_ENV === 'production' 
      ? ['https://vanishpost.com', 'https://www.vanishpost.com']
      : ['http://localhost:3000'],
    credentials: true,
    optionsSuccessStatus: 200
  }
};

// Database optimization settings
export const DATABASE_CONFIG = {
  // Connection settings
  CONNECTION: {
    maxConnections: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 10000
  },
  
  // Query optimization
  QUERIES: {
    // Use prepared statements
    usePreparedStatements: true,
    
    // Query timeout (in milliseconds)
    queryTimeout: 30000,
    
    // Batch size for bulk operations
    batchSize: 100,
    
    // Enable query result caching
    enableQueryCache: true,
    
    // Query cache TTL (in seconds)
    queryCacheTTL: 300
  },
  
  // Cleanup settings
  CLEANUP: {
    // Clean up expired records every hour
    expiredRecordsCleanupInterval: 3600,
    
    // Clean up old audit logs every day
    auditLogCleanupInterval: 86400,
    
    // Keep audit logs for 90 days
    auditLogRetentionDays: 90,
    
    // Keep generated records for 7 days after expiration
    recordRetentionDays: 7
  }
};

// Monitoring configuration
export const MONITORING_CONFIG = {
  // Health check intervals (in seconds)
  HEALTH_CHECK_INTERVAL: 60,
  
  // Metrics collection interval (in seconds)
  METRICS_INTERVAL: 30,
  
  // Performance sampling rate (0.0 to 1.0)
  PERFORMANCE_SAMPLING_RATE: 0.1,
  
  // Error tracking
  ERROR_TRACKING: {
    // Sample rate for error reporting
    sampleRate: 1.0,
    
    // Maximum error stack trace length
    maxStackTraceLength: 2000,
    
    // Include request details in error reports
    includeRequestDetails: true
  },
  
  // Alerting thresholds
  ALERTS: {
    // Alert if error rate exceeds threshold
    errorRateThreshold: 5,
    
    // Alert if response time exceeds threshold
    responseTimeThreshold: 5000,
    
    // Alert if memory usage exceeds threshold
    memoryThreshold: 512,
    
    // Alert if disk usage exceeds threshold
    diskThreshold: 80
  }
};

// Feature flags for gradual rollout
export const FEATURE_FLAGS = {
  // Enable new DKIM key generation algorithm
  ENABLE_NEW_DKIM_ALGORITHM: false,
  
  // Enable enhanced DNS validation
  ENABLE_ENHANCED_DNS_VALIDATION: true,
  
  // Enable advanced DMARC features
  ENABLE_ADVANCED_DMARC_FEATURES: true,
  
  // Enable real-time validation
  ENABLE_REALTIME_VALIDATION: true,
  
  // Enable batch operations
  ENABLE_BATCH_OPERATIONS: false,
  
  // Enable API versioning
  ENABLE_API_VERSIONING: false
};

// Export all configurations
export const CONFIG = {
  CACHE: CACHE_CONFIG,
  RATE_LIMIT: RATE_LIMIT_CONFIG,
  PERFORMANCE: PERFORMANCE_THRESHOLDS,
  RESOURCE: RESOURCE_CONFIG,
  OPTIMIZATION: OPTIMIZATION_FLAGS,
  SECURITY: SECURITY_CONFIG,
  DATABASE: DATABASE_CONFIG,
  MONITORING: MONITORING_CONFIG,
  FEATURES: FEATURE_FLAGS
};

// Environment-specific overrides
if (process.env.NODE_ENV === 'production') {
  // Production optimizations
  CONFIG.OPTIMIZATION.ENABLE_DETAILED_LOGGING = false;
  CONFIG.PERFORMANCE.DKIM_GENERATION_MAX_TIME = 8000;
  CONFIG.RATE_LIMIT.DKIM_GENERATION.maxRequests = 5;
  CONFIG.MONITORING.PERFORMANCE_SAMPLING_RATE = 0.05;
} else if (process.env.NODE_ENV === 'development') {
  // Development settings
  CONFIG.RATE_LIMIT.DKIM_GENERATION.maxRequests = 100;
  CONFIG.RATE_LIMIT.DMARC_GENERATION.maxRequests = 100;
  CONFIG.MONITORING.PERFORMANCE_SAMPLING_RATE = 1.0;
}

export default CONFIG;
