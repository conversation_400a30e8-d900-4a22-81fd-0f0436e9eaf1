/**
 * API route for managing the cleanup scheduler
 * 
 * This API provides functionality to start, stop, and check the status of the cleanup scheduler.
 */
import { NextRequest, NextResponse } from 'next/server';
import { logInfo, logError } from '@/lib/logging';
import { 
  startCleanupScheduler, 
  stopCleanupScheduler, 
  isCleanupSchedulerRunning 
} from '@/lib/cleanup/cleanupScheduler';
import { getConfig, updateConfig } from '@/lib/config/configService';

/**
 * POST /api/management-portal-x7z9y2/cleanup/scheduler
 * 
 * Start or stop the cleanup scheduler
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, intervalMinutes } = body;
    
    if (action === 'start') {
      // Validate interval
      if (!intervalMinutes || intervalMinutes <= 0) {
        return NextResponse.json({
          success: false,
          error: 'Invalid cleanup interval'
        }, { status: 400 });
      }
      
      // Update configuration
      await updateConfig('cleanupIntervalMinutes', intervalMinutes);
      
      // Start scheduler
      const started = await startCleanupScheduler();
      
      if (!started) {
        return NextResponse.json({
          success: false,
          error: 'Failed to start cleanup scheduler'
        }, { status: 500 });
      }
      
      logInfo('cleanup', 'Cleanup scheduler started', { intervalMinutes });
      
      return NextResponse.json({
        success: true,
        message: 'Cleanup scheduler started',
        intervalMinutes
      });
    } else if (action === 'stop') {
      // Stop scheduler
      stopCleanupScheduler();
      
      logInfo('cleanup', 'Cleanup scheduler stopped');
      
      return NextResponse.json({
        success: true,
        message: 'Cleanup scheduler stopped'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }
  } catch (error) {
    logError('cleanup', 'Error managing cleanup scheduler', { error });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

/**
 * GET /api/management-portal-x7z9y2/cleanup/scheduler
 * 
 * Get the status of the cleanup scheduler
 */
export async function GET(request: NextRequest) {
  try {
    const running = isCleanupSchedulerRunning();
    const intervalMinutes = await getConfig('cleanupIntervalMinutes');
    
    // Calculate next run time if running
    let nextRunAt = null;
    if (running && intervalMinutes) {
      nextRunAt = new Date(Date.now() + intervalMinutes * 60 * 1000).toISOString();
    }
    
    return NextResponse.json({
      success: true,
      status: {
        running,
        intervalMinutes,
        nextRunAt
      }
    });
  } catch (error) {
    logError('cleanup', 'Error getting cleanup scheduler status', { error });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
