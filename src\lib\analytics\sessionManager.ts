/**
 * Session Management Service for VanishPost Analytics
 * 
 * This service handles session creation, tracking, and management for analytics purposes.
 * Sessions are used to group user interactions and calculate engagement metrics.
 */

import { createServerSupabaseClient } from '@/lib/supabase';
import { logError, logInfo } from '@/lib/logging';

export interface SessionData {
  sessionId: string;
  userId?: string;
  sessionStartTime: Date;
  sessionEndTime?: Date;
  sessionDuration?: number; // in seconds
  emailsGeneratedCount: number;
  emailsReceivedCount: number;
  emailsViewedCount: number;
  emailsDeletedCount: number;
  manualRefreshCount: number;
  copyActionsCount: number;
  addressRegenerationCount: number;
  deviceType?: string;
  browser?: string;
  country?: string;
  referrer?: string;
}

export interface SessionMetrics {
  emailsGeneratedCount?: number;
  emailsReceivedCount?: number;
  emailsViewedCount?: number;
  emailsDeletedCount?: number;
  manualRefreshCount?: number;
  copyActionsCount?: number;
  addressRegenerationCount?: number;
}

export interface SessionMetadata {
  deviceType?: string;
  browser?: string;
  country?: string;
  referrer?: string;
  userId?: string;
}

/**
 * Generate a unique session ID
 * Format: timestamp-random-suffix for uniqueness and sortability
 */
export function generateSessionId(): string {
  const timestamp = Date.now().toString(36);
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  return `session_${timestamp}_${randomSuffix}`;
}

/**
 * Get or create a session in the database
 * If sessionId exists, return existing session data
 * If not, create a new session record
 */
export async function getOrCreateSession(
  sessionId: string, 
  metadata?: SessionMetadata
): Promise<SessionData | null> {
  try {
    const supabase = createServerSupabaseClient();
    
    // First, try to get existing session
    const { data: existingSession, error: fetchError } = await supabase
      .from('session_analytics')
      .select('*')
      .eq('session_id', sessionId)
      .single();

    if (existingSession && !fetchError) {
      // Convert database record to SessionData format
      return {
        sessionId: existingSession.session_id,
        userId: existingSession.user_id || undefined,
        sessionStartTime: new Date(existingSession.session_start_time),
        sessionEndTime: existingSession.session_end_time ? new Date(existingSession.session_end_time) : undefined,
        sessionDuration: existingSession.session_duration || undefined,
        emailsGeneratedCount: existingSession.emails_generated_count || 0,
        emailsReceivedCount: existingSession.emails_received_count || 0,
        emailsViewedCount: existingSession.emails_viewed_count || 0,
        emailsDeletedCount: existingSession.emails_deleted_count || 0,
        manualRefreshCount: existingSession.manual_refresh_count || 0,
        copyActionsCount: existingSession.copy_actions_count || 0,
        addressRegenerationCount: existingSession.address_regeneration_count || 0,
        deviceType: existingSession.device_type || undefined,
        browser: existingSession.browser || undefined,
        country: existingSession.country || undefined,
        referrer: existingSession.referrer || undefined,
      };
    }

    // Session doesn't exist, create a new one
    const sessionStartTime = new Date();
    const newSessionData = {
      session_id: sessionId,
      user_id: metadata?.userId || null,
      session_start_time: sessionStartTime.toISOString(),
      emails_generated_count: 0,
      emails_received_count: 0,
      emails_viewed_count: 0,
      emails_deleted_count: 0,
      manual_refresh_count: 0,
      copy_actions_count: 0,
      address_regeneration_count: 0,
      device_type: metadata?.deviceType || null,
      browser: metadata?.browser || null,
      country: metadata?.country || null,
      referrer: metadata?.referrer || null,
    };

    const { data: newSession, error: createError } = await supabase
      .from('session_analytics')
      .insert(newSessionData)
      .select()
      .single();

    if (createError) {
      logError('SessionManager', `Failed to create session ${sessionId}`, { error: createError });
      return null;
    }

    logInfo('SessionManager', `Created new session ${sessionId}`, { metadata });

    return {
      sessionId: newSession.session_id,
      userId: newSession.user_id || undefined,
      sessionStartTime: new Date(newSession.session_start_time),
      emailsGeneratedCount: 0,
      emailsReceivedCount: 0,
      emailsViewedCount: 0,
      emailsDeletedCount: 0,
      manualRefreshCount: 0,
      copyActionsCount: 0,
      addressRegenerationCount: 0,
      deviceType: newSession.device_type || undefined,
      browser: newSession.browser || undefined,
      country: newSession.country || undefined,
      referrer: newSession.referrer || undefined,
    };




  } catch (error) {
    logError('SessionManager', `Error in getOrCreateSession for ${sessionId}`, { error });
    return null;
  }
}

/**
 * Update session metrics in the database
 * Increments the specified metrics by the provided values
 */
export async function updateSessionMetrics(
  sessionId: string,
  metrics: SessionMetrics
): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();

    // For now, let's use a simpler approach with direct updates
    // We'll fetch current values and increment them
    const { data: currentSession, error: fetchError } = await supabase
      .from('session_analytics')
      .select('*')
      .eq('session_id', sessionId)
      .single();

    if (fetchError || !currentSession) {
      logError('SessionManager', `Session ${sessionId} not found for metrics update`, { error: fetchError });
      return false;
    }

    // Calculate new values
    const newMetrics = {
      emails_generated_count: (currentSession.emails_generated_count || 0) + (metrics.emailsGeneratedCount || 0),
      emails_received_count: (currentSession.emails_received_count || 0) + (metrics.emailsReceivedCount || 0),
      emails_viewed_count: (currentSession.emails_viewed_count || 0) + (metrics.emailsViewedCount || 0),
      emails_deleted_count: (currentSession.emails_deleted_count || 0) + (metrics.emailsDeletedCount || 0),
      manual_refresh_count: (currentSession.manual_refresh_count || 0) + (metrics.manualRefreshCount || 0),
      copy_actions_count: (currentSession.copy_actions_count || 0) + (metrics.copyActionsCount || 0),
      address_regeneration_count: (currentSession.address_regeneration_count || 0) + (metrics.addressRegenerationCount || 0),
      updated_at: new Date().toISOString(),
    };

    const { error: updateError } = await supabase
      .from('session_analytics')
      .update(newMetrics)
      .eq('session_id', sessionId);

    if (updateError) {
      logError('SessionManager', `Failed to update metrics for session ${sessionId}`, { error: updateError, metrics });
      return false;
    }

    logInfo('SessionManager', `Updated metrics for session ${sessionId}`, { metrics });
    return true;

  } catch (error) {
    logError('SessionManager', `Error updating session metrics for ${sessionId}`, { error, metrics });
    return false;
  }
}

/**
 * Reconcile session analytics with actual analytics events
 * This function helps fix discrepancies between event tracking and session aggregation
 */
export async function reconcileSessionAnalytics(sessionId: string): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();

    // Get all analytics events for this session
    const { data: events, error: eventsError } = await supabase
      .from('analytics_events')
      .select('event_type')
      .eq('session_id', sessionId);

    if (eventsError) {
      logError('SessionManager', `Failed to fetch events for reconciliation of session ${sessionId}`, { error: eventsError });
      return false;
    }

    // Count events by type
    const eventCounts = events?.reduce((acc, event) => {
      acc[event.event_type] = (acc[event.event_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Calculate correct metrics based on actual events
    const correctMetrics = {
      emails_generated_count: eventCounts['email_address_generated'] || 0,
      emails_received_count: eventCounts['email_received'] || 0,
      emails_viewed_count: eventCounts['email_opened'] || 0,
      emails_deleted_count: eventCounts['email_deleted'] || 0,
      manual_refresh_count: eventCounts['manual_refresh_triggered'] || 0,
      copy_actions_count: eventCounts['email_address_copied'] || 0,
      updated_at: new Date().toISOString(),
    };

    // Update session with correct metrics
    const { error: updateError } = await supabase
      .from('session_analytics')
      .update(correctMetrics)
      .eq('session_id', sessionId);

    if (updateError) {
      logError('SessionManager', `Failed to reconcile session ${sessionId}`, { error: updateError });
      return false;
    }

    logInfo('SessionManager', `Reconciled session ${sessionId}`, {
      eventCounts,
      correctMetrics
    });
    return true;

  } catch (error) {
    logError('SessionManager', `Error reconciling session ${sessionId}`, { error });
    return false;
  }
}

/**
 * End a session by setting the end time and calculating duration
 */
export async function endSession(sessionId: string): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient();

    // Get current session to calculate duration
    const { data: currentSession, error: fetchError } = await supabase
      .from('session_analytics')
      .select('session_start_time')
      .eq('session_id', sessionId)
      .single();

    if (fetchError || !currentSession) {
      logError('SessionManager', `Session ${sessionId} not found for ending`, { error: fetchError });
      return false;
    }

    const sessionEndTime = new Date();
    const sessionStartTime = new Date(currentSession.session_start_time);
    const sessionDuration = Math.floor((sessionEndTime.getTime() - sessionStartTime.getTime()) / 1000); // in seconds

    const { error: updateError } = await supabase
      .from('session_analytics')
      .update({
        session_end_time: sessionEndTime.toISOString(),
        session_duration: sessionDuration,
        updated_at: sessionEndTime.toISOString(),
      })
      .eq('session_id', sessionId);

    if (updateError) {
      logError('SessionManager', `Failed to end session ${sessionId}`, { error: updateError });
      return false;
    }

    logInfo('SessionManager', `Ended session ${sessionId}`, { duration: sessionDuration });
    return true;
  } catch (error) {
    logError('SessionManager', `Error ending session ${sessionId}`, { error });
    return false;
  }
}

/**
 * Get session duration in seconds
 * If session is still active, calculates duration from start time to now
 * If session is ended, returns the stored duration
 */
export async function getSessionDuration(sessionId: string): Promise<number> {
  try {
    const supabase = createServerSupabaseClient();

    const { data: session, error } = await supabase
      .from('session_analytics')
      .select('session_start_time, session_end_time, session_duration')
      .eq('session_id', sessionId)
      .single();

    if (error || !session) {
      logError('SessionManager', `Session ${sessionId} not found for duration calculation`, { error });
      return 0;
    }

    // If session has ended, return stored duration
    if (session.session_end_time && session.session_duration) {
      return session.session_duration;
    }

    // Calculate current duration for active session
    const sessionStartTime = new Date(session.session_start_time);
    const currentTime = new Date();
    const duration = Math.floor((currentTime.getTime() - sessionStartTime.getTime()) / 1000);

    return duration;

  } catch (error) {
    logError('SessionManager', `Error calculating session duration for ${sessionId}`, { error });
    return 0;
  }
}

/**
 * Get session data by session ID
 */
export async function getSession(sessionId: string): Promise<SessionData | null> {
  try {
    const supabase = createServerSupabaseClient();

    const { data: session, error } = await supabase
      .from('session_analytics')
      .select('*')
      .eq('session_id', sessionId)
      .single();

    if (error || !session) {
      return null;
    }

    return {
      sessionId: session.session_id,
      userId: session.user_id || undefined,
      sessionStartTime: new Date(session.session_start_time),
      sessionEndTime: session.session_end_time ? new Date(session.session_end_time) : undefined,
      sessionDuration: session.session_duration || undefined,
      emailsGeneratedCount: session.emails_generated_count || 0,
      emailsReceivedCount: session.emails_received_count || 0,
      emailsViewedCount: session.emails_viewed_count || 0,
      emailsDeletedCount: session.emails_deleted_count || 0,
      manualRefreshCount: session.manual_refresh_count || 0,
      copyActionsCount: session.copy_actions_count || 0,
      addressRegenerationCount: session.address_regeneration_count || 0,
      deviceType: session.device_type || undefined,
      browser: session.browser || undefined,
      country: session.country || undefined,
      referrer: session.referrer || undefined,
    };

  } catch (error) {
    logError('SessionManager', `Error getting session ${sessionId}`, { error });
    return null;
  }
}
