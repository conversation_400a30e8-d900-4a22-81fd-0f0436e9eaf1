/**
 * Data Reconciliation System for VanishPost Analytics
 * 
 * This system detects and fixes discrepancies between different analytics data sources:
 * - temp_emails table vs analytics events
 * - analytics events vs session analytics
 * - Real-time data vs cached data
 */

import { createServerSupabaseClient } from '@/lib/supabase';
import { logError, logInfo, logWarn } from '@/lib/logging';
import { reconcileSessionAnalytics } from './sessionManager';

export interface DataDiscrepancy {
  type: 'email_generation' | 'session_aggregation' | 'cache_mismatch';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  actualValue: number;
  expectedValue: number;
  difference: number;
  percentageDiff: number;
  source: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface ReconciliationReport {
  timestamp: string;
  totalChecks: number;
  discrepanciesFound: number;
  discrepancies: DataDiscrepancy[];
  autoFixedCount: number;
  manualFixRequired: number;
  summary: {
    emailGenerationGap: number;
    sessionAggregationErrors: number;
    cacheInconsistencies: number;
  };
}

/**
 * Run comprehensive data reconciliation
 */
export async function runDataReconciliation(
  options: {
    autoFix?: boolean;
    dateRange?: { start: string; end: string };
    includeCache?: boolean;
  } = {}
): Promise<ReconciliationReport> {
  const { autoFix = false, dateRange, includeCache = true } = options;
  
  logInfo('DataReconciliation', 'Starting comprehensive data reconciliation', { options });

  const discrepancies: DataDiscrepancy[] = [];
  let autoFixedCount = 0;
  let totalChecks = 0;

  try {
    // 1. Check email generation discrepancies
    const emailDiscrepancies = await checkEmailGenerationDiscrepancies(dateRange);
    discrepancies.push(...emailDiscrepancies);
    totalChecks += 1;

    // 2. Check session aggregation discrepancies
    const sessionDiscrepancies = await checkSessionAggregationDiscrepancies(dateRange);
    discrepancies.push(...sessionDiscrepancies);
    totalChecks += 1;

    // 3. Auto-fix session aggregation issues if requested
    if (autoFix) {
      const fixedSessions = await autoFixSessionAggregation();
      autoFixedCount += fixedSessions;
    }

    // 4. Check cache inconsistencies if requested
    if (includeCache) {
      const cacheDiscrepancies = await checkCacheConsistency();
      discrepancies.push(...cacheDiscrepancies);
      totalChecks += 1;
    }

    const report: ReconciliationReport = {
      timestamp: new Date().toISOString(),
      totalChecks,
      discrepanciesFound: discrepancies.length,
      discrepancies,
      autoFixedCount,
      manualFixRequired: discrepancies.filter(d => d.severity === 'high' || d.severity === 'critical').length,
      summary: {
        emailGenerationGap: discrepancies.filter(d => d.type === 'email_generation').length,
        sessionAggregationErrors: discrepancies.filter(d => d.type === 'session_aggregation').length,
        cacheInconsistencies: discrepancies.filter(d => d.type === 'cache_mismatch').length,
      }
    };

    logInfo('DataReconciliation', 'Data reconciliation completed', {
      discrepanciesFound: report.discrepanciesFound,
      autoFixedCount: report.autoFixedCount,
      summary: report.summary
    });

    return report;

  } catch (error) {
    logError('DataReconciliation', 'Error during data reconciliation', { error });
    throw error;
  }
}

/**
 * Check discrepancies between temp_emails and analytics events
 */
async function checkEmailGenerationDiscrepancies(
  dateRange?: { start: string; end: string }
): Promise<DataDiscrepancy[]> {
  try {
    const supabase = createServerSupabaseClient();
    const discrepancies: DataDiscrepancy[] = [];

    // Get date range for comparison
    const endDate = dateRange?.end ? new Date(dateRange.end) : new Date();
    const startDate = dateRange?.start ? new Date(dateRange.start) : new Date(endDate.getTime() - 24 * 60 * 60 * 1000);

    // Count actual emails created
    const { count: actualEmails, error: emailError } = await supabase
      .from('temp_emails')
      .select('*', { count: 'exact', head: true })
      .gte('creation_time', startDate.toISOString())
      .lte('creation_time', endDate.toISOString());

    if (emailError) {
      throw emailError;
    }

    // Count tracked email generation events
    const { count: trackedEmails, error: eventError } = await supabase
      .from('analytics_events')
      .select('*', { count: 'exact', head: true })
      .eq('event_type', 'email_address_generated')
      .gte('timestamp', startDate.toISOString())
      .lte('timestamp', endDate.toISOString());

    if (eventError) {
      throw eventError;
    }

    const actualCount = actualEmails || 0;
    const trackedCount = trackedEmails || 0;
    const difference = actualCount - trackedCount;
    const percentageDiff = actualCount > 0 ? (difference / actualCount) * 100 : 0;

    if (Math.abs(difference) > 0) {
      const severity = percentageDiff > 50 ? 'critical' : 
                      percentageDiff > 25 ? 'high' : 
                      percentageDiff > 10 ? 'medium' : 'low';

      discrepancies.push({
        type: 'email_generation',
        severity,
        description: `Email generation tracking gap: ${difference} emails not tracked`,
        actualValue: actualCount,
        expectedValue: trackedCount,
        difference,
        percentageDiff,
        source: 'temp_emails vs analytics_events',
        timestamp: new Date().toISOString(),
        metadata: {
          dateRange: { start: startDate.toISOString(), end: endDate.toISOString() },
          actualEmails: actualCount,
          trackedEmails: trackedCount
        }
      });
    }

    return discrepancies;

  } catch (error) {
    logError('DataReconciliation', 'Error checking email generation discrepancies', { error });
    return [];
  }
}

/**
 * Check discrepancies between analytics events and session aggregation
 */
async function checkSessionAggregationDiscrepancies(
  dateRange?: { start: string; end: string }
): Promise<DataDiscrepancy[]> {
  try {
    const supabase = createServerSupabaseClient();
    const discrepancies: DataDiscrepancy[] = [];

    // Get date range for comparison
    const endDate = dateRange?.end ? new Date(dateRange.end) : new Date();
    const startDate = dateRange?.start ? new Date(dateRange.start) : new Date(endDate.getTime() - 24 * 60 * 60 * 1000);

    // Get sessions in date range
    const { data: sessions, error: sessionError } = await supabase
      .from('session_analytics')
      .select('session_id, emails_generated_count, emails_received_count, emails_viewed_count, copy_actions_count')
      .gte('session_start_time', startDate.toISOString())
      .lte('session_start_time', endDate.toISOString());

    if (sessionError) {
      throw sessionError;
    }

    // Check each session for discrepancies
    for (const session of sessions || []) {
      const sessionDiscrepancies = await checkSingleSessionDiscrepancy(session.session_id);
      discrepancies.push(...sessionDiscrepancies);
    }

    return discrepancies;

  } catch (error) {
    logError('DataReconciliation', 'Error checking session aggregation discrepancies', { error });
    return [];
  }
}

/**
 * Check discrepancies for a single session
 */
async function checkSingleSessionDiscrepancy(sessionId: string): Promise<DataDiscrepancy[]> {
  try {
    const supabase = createServerSupabaseClient();
    const discrepancies: DataDiscrepancy[] = [];

    // Get session data
    const { data: sessionData, error: sessionError } = await supabase
      .from('session_analytics')
      .select('*')
      .eq('session_id', sessionId)
      .single();

    if (sessionError || !sessionData) {
      return [];
    }

    // Get actual events for this session
    const { data: events, error: eventsError } = await supabase
      .from('analytics_events')
      .select('event_type')
      .eq('session_id', sessionId);

    if (eventsError) {
      return [];
    }

    // Count events by type
    const eventCounts = events?.reduce((acc, event) => {
      acc[event.event_type] = (acc[event.event_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Check each metric
    const checks = [
      {
        metric: 'emails_generated',
        eventType: 'email_address_generated',
        sessionValue: sessionData.emails_generated_count || 0,
        eventValue: eventCounts['email_address_generated'] || 0
      },
      {
        metric: 'emails_received',
        eventType: 'email_received',
        sessionValue: sessionData.emails_received_count || 0,
        eventValue: eventCounts['email_received'] || 0
      },
      {
        metric: 'emails_viewed',
        eventType: 'email_opened',
        sessionValue: sessionData.emails_viewed_count || 0,
        eventValue: eventCounts['email_opened'] || 0
      },
      {
        metric: 'copy_actions',
        eventType: 'email_address_copied',
        sessionValue: sessionData.copy_actions_count || 0,
        eventValue: eventCounts['email_address_copied'] || 0
      }
    ];

    for (const check of checks) {
      const difference = check.eventValue - check.sessionValue;
      if (Math.abs(difference) > 0) {
        const percentageDiff = check.eventValue > 0 ? Math.abs(difference / check.eventValue) * 100 : 0;
        const severity = percentageDiff > 50 ? 'high' : percentageDiff > 25 ? 'medium' : 'low';

        discrepancies.push({
          type: 'session_aggregation',
          severity,
          description: `Session ${sessionId} ${check.metric} mismatch: ${difference} difference`,
          actualValue: check.eventValue,
          expectedValue: check.sessionValue,
          difference,
          percentageDiff,
          source: `session_analytics vs analytics_events`,
          timestamp: new Date().toISOString(),
          metadata: {
            sessionId,
            metric: check.metric,
            eventType: check.eventType
          }
        });
      }
    }

    return discrepancies;

  } catch (error) {
    logError('DataReconciliation', `Error checking session ${sessionId} discrepancy`, { error });
    return [];
  }
}

/**
 * Auto-fix session aggregation issues
 */
async function autoFixSessionAggregation(): Promise<number> {
  try {
    const supabase = createServerSupabaseClient();
    let fixedCount = 0;

    // Get all sessions that might need fixing (recent ones)
    const { data: sessions, error: sessionError } = await supabase
      .from('session_analytics')
      .select('session_id')
      .gte('session_start_time', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
      .order('session_start_time', { ascending: false })
      .limit(100);

    if (sessionError) {
      throw sessionError;
    }

    // Reconcile each session
    for (const session of sessions || []) {
      const success = await reconcileSessionAnalytics(session.session_id);
      if (success) {
        fixedCount++;
      }
    }

    logInfo('DataReconciliation', `Auto-fixed ${fixedCount} sessions`);
    return fixedCount;

  } catch (error) {
    logError('DataReconciliation', 'Error during auto-fix session aggregation', { error });
    return 0;
  }
}

/**
 * Check cache consistency (placeholder for future implementation)
 */
async function checkCacheConsistency(): Promise<DataDiscrepancy[]> {
  // This would check if cached analytics data matches database data
  // For now, return empty array as cache consistency checks are not critical
  return [];
}
