/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    // Add a client-safe admin path that's different from your actual admin path
    // This is what will be exposed to the client-side code
    NEXT_PUBLIC_ADMIN_UI_PATH: 'admin-portal',
  },

  // Webpack configuration for better module resolution and CSS optimization
  webpack: (config, { isServer, dev }) => {
    // Ensure proper module resolution for TypeScript files
    config.resolve.extensionAlias = {
      '.js': ['.ts', '.tsx', '.js', '.jsx'],
      '.mjs': ['.mts', '.mjs'],
      '.cjs': ['.cts', '.cjs']
    };

    // Add fallback for better module resolution
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      os: false,
    };

    // Optimize CSS loading to reduce preload warnings
    if (!dev && !isServer) {
      // Improve CSS chunk splitting to reduce preload warnings
      config.optimization.splitChunks.cacheGroups = {
        ...config.optimization.splitChunks.cacheGroups,
        styles: {
          name: 'styles',
          test: /\.(css|scss|sass)$/,
          chunks: 'all',
          enforce: true,
          priority: 10,
        },
      };
    }

    return config;
  },

  // Experimental features for package optimization
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },

  // Add URL rewrites to map the public path to the secure path
  async rewrites() {
    return [
      // Rewrite client-visible admin-portal path to the actual secure path
      {
        source: '/admin-portal/:path*',
        destination: '/management-portal-x7z9y2/:path*',
      },
      // Rewrite API calls to the admin path
      {
        source: '/api/admin-portal/:path*',
        destination: '/api/management-portal-x7z9y2/:path*',
      },
      // Specific rewrite for admin-portal login
      {
        source: '/admin-portal/login',
        destination: '/management-portal-x7z9y2/login',
      },

    ];
  },

  async headers() {
    // Different CSP for development and production
    // Enhanced CSP with proper frame-ancestors and report-uri to handle violations gracefully
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Base CSP directives
    const baseCSP = {
      'default-src': "'self'",
      'script-src': "'self' 'unsafe-inline' 'unsafe-eval' https://pagead2.googlesyndication.com https://partner.googleadservices.com https://www.googletagservices.com https://adservice.google.com https://tpc.googlesyndication.com https://www.google-analytics.com https://*.adtrafficquality.google https://us.i.posthog.com https://*.i.posthog.com https://us-assets.i.posthog.com https://va.vercel-scripts.com",
      'style-src': "'self' 'unsafe-inline' https://fonts.googleapis.com",
      'font-src': "'self' https://fonts.gstatic.com",
      'img-src': "'self' data: https: http:",
      'connect-src': "'self' https://*.supabase.co wss://*.supabase.co ws: wss: https://pagead2.googlesyndication.com https://www.google-analytics.com https://*.adtrafficquality.google https://*.doubleclick.net https://us.i.posthog.com https://*.i.posthog.com https://us-assets.i.posthog.com https://va.vercel-scripts.com https://vitals.vercel-insights.com",
      'frame-src': "'self' data: https: http: https://googleads.g.doubleclick.net https://tpc.googlesyndication.com",
      'frame-ancestors': "'self'", // Prevent VanishPost from being embedded in other sites
      'object-src': "'none'", // Prevent object/embed elements for security
      'base-uri': "'self'", // Restrict base tag URLs
      'form-action': "'self'" // Restrict form submissions
    };

    // Add development-specific CSP relaxations
    if (isDevelopment) {
      // In development, we can be more permissive for debugging
      baseCSP['report-uri'] = '/api/csp-report'; // Optional: endpoint to handle CSP reports
    }

    // Convert CSP object to string
    const cspValue = Object.entries(baseCSP)
      .map(([directive, value]) => `${directive} ${value}`)
      .join('; ') + ';';

    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: cspValue
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload'
          }
        ]
      }
    ];
  }
};

module.exports = nextConfig;
