'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { AuthProvider, useAuth } from '@/lib/auth/AuthContext';

function AdminLayoutContent({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, logout, isLoading } = useAuth();
  const pathname = usePathname() || '';
  const router = useRouter();

  // If not authenticated and not on login page, redirect to login
  React.useEffect(() => {
    // Only redirect after loading is complete
    if (!isLoading && !isAuthenticated && pathname !== '/admin/login') {
      console.log('Not authenticated, redirecting to login');
      router.push('/admin/login');
    }
  }, [isAuthenticated, isLoading, pathname, router]);

  // If on login page, don't show the header
  if (pathname === '/admin/login') {
    return <>{children}</>;
  }

  // If not authenticated and not loading, show nothing while redirecting
  if (!isAuthenticated && !isLoading) {
    return null;
  }

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md transition-all duration-300 animate-fadeIn">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mb-4"></div>
            <p className="text-gray-600">Loading admin panel...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* Sidebar */}
      <div className="bg-indigo-800 text-white w-64 flex-shrink-0 min-h-screen shadow-lg transition-all duration-300">
        <div className="p-4 border-b border-indigo-700 flex items-center space-x-2">
          <Image
            src="/vanishpost-temporary-email-logo.svg"
            alt="VanishPost Secure Temporary Email Service Admin"
            width={24}
            height={24}
            className="h-6 w-6"
          />
          <h1 className="text-xl font-bold">VanishPost Admin</h1>
        </div>
        <nav className="mt-5">
          <div className="px-4 py-2 text-xs font-semibold text-indigo-300 uppercase tracking-wider">
            Dashboard
          </div>


          <div className="px-4 py-2 mt-6 text-xs font-semibold text-indigo-300 uppercase tracking-wider">
            Configuration
          </div>
          <Link
            href="/admin/domains"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname.startsWith('/admin/domains') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" />
            </svg>
            Domains
          </Link>
          <Link
            href="/admin/config"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname.startsWith('/admin/config') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
            </svg>
            Settings
          </Link>
          <Link
            href="/admin/setup"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname.startsWith('/admin/setup') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
            </svg>
            Setup
          </Link>

          <div className="px-4 py-2 mt-6 text-xs font-semibold text-indigo-300 uppercase tracking-wider">
            Monitoring
          </div>
          <Link
            href="/admin/health"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname.startsWith('/admin/health') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
            </svg>
            Health
          </Link>
          <Link
            href="/admin/monitoring"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname.startsWith('/admin/monitoring') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
            Monitoring
          </Link>
          <Link
            href="/admin/cleanup"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname.startsWith('/admin/cleanup') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            Cleanup
          </Link>

          <div className="px-4 py-2 mt-6 text-xs font-semibold text-indigo-300 uppercase tracking-wider">
            Advertising
          </div>
          <Link
            href="/admin/ads"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname === '/admin/ads' ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
            Ad Management
          </Link>
          <Link
            href="/admin/ads/performance"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname === '/admin/ads/performance' ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" />
              <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" />
            </svg>
            Ad Performance
          </Link>
          <Link
            href="/admin/ads/demo"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname === '/admin/ads/demo' ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
            </svg>
            Ad Demo
          </Link>
          <Link
            href="/admin/ads/optimization"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname === '/admin/ads/optimization' ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
            </svg>
            Optimization
          </Link>

          <div className="px-4 py-2 mt-6 text-xs font-semibold text-indigo-300 uppercase tracking-wider">
            User Management
          </div>
          <Link
            href="/admin/users"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname === '/admin/users' ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
            </svg>
            Manage Users
          </Link>
          <Link
            href="/admin/users/activity"
            className={`flex items-center px-4 py-3 text-sm font-medium ${pathname === '/admin/users/activity' ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700'}`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
            User Activity
          </Link>

          <div className="px-4 py-2 mt-6 text-xs font-semibold text-indigo-300 uppercase tracking-wider">
            Account
          </div>
          <button
            onClick={async () => {
              await logout();
              router.push('/admin/login');
            }}
            className="flex items-center w-full text-left px-4 py-3 text-sm font-medium text-indigo-100 hover:bg-indigo-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
            </svg>
            Logout
          </button>
          <Link
            href="/"
            className="flex items-center px-4 py-3 text-sm font-medium text-indigo-100 hover:bg-indigo-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
            </svg>
            Back to Site
          </Link>
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm z-10 transition-all duration-300">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                {pathname === '/admin/analytics' && 'Analytics Dashboard'}
                {pathname === '/admin/domains' && 'Domain Management'}
                {pathname === '/admin/config' && 'System Configuration'}
                {pathname === '/admin/setup' && 'System Setup'}
                {pathname === '/admin/health' && 'System Health'}
                {pathname === '/admin/monitoring' && 'System Monitoring'}
                {pathname === '/admin/cleanup' && 'Cleanup Management'}
                {pathname === '/admin/ads' && 'Ad Management'}
                {pathname === '/admin/ads/performance' && 'Ad Performance'}
                {pathname === '/admin/ads/demo' && 'Ad Demo'}
                {pathname === '/admin/ads/optimization' && 'Ad Optimization'}
                {pathname === '/admin/users' && 'User Management'}
                {pathname === '/admin/users/activity' && 'User Activity'}
                {![
                  '/admin/domains',
                  '/admin/config',
                  '/admin/setup',
                  '/admin/health',
                  '/admin/monitoring',
                  '/admin/cleanup',
                  '/admin/ads',
                  '/admin/ads/performance',
                  '/admin/ads/demo',
                  '/admin/ads/optimization',
                  '/admin/users',
                  '/admin/users/activity'
                ].includes(pathname) && 'Admin Dashboard'}
              </h1>
              <nav className="flex mt-1" aria-label="Breadcrumb">
                <ol className="flex items-center space-x-1 text-xs text-gray-500">
                  <li>
                    <Link href="/admin" className="hover:text-indigo-600 transition-colors duration-200">Admin</Link>
                  </li>
                  <li className="flex items-center">
                    <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="ml-1 font-medium text-gray-700">
                      {pathname.split('/').pop()?.replace(/^\w/, (c) => c.toUpperCase())}
                    </span>
                  </li>
                </ol>
              </nav>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, Admin</span>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-auto bg-gray-100 transition-all duration-300">
          <div className="py-6 animate-fadeIn">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthProvider>
      <AdminLayoutContent>
        {children}
      </AdminLayoutContent>
    </AuthProvider>
  );
}
