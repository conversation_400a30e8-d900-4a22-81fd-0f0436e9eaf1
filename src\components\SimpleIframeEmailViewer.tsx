'use client';

import { useEffect, useState } from 'react';
import { Email } from '@/lib/emailProcessing';

import SimpleIframeRenderer from './SimpleIframeRenderer';
import AnimatedEnvelope from './AnimatedEnvelope';
import '@/styles/envelope-animation.css';

interface SimpleIframeEmailViewerProps {
  /** The selected email to display */
  selectedEmail: Email | null;

  /** The current email address */
  emailAddress: string;

  /** Whether emails are currently loading */
  isLoading: boolean;

  /** Function to format file size */
  formatFileSize: (bytes: number) => string;

  /** Function to go back to the inbox (mobile only) */
  onBackToInbox: () => void;

  /** Whether to show the inbox on mobile */
  showMobileInbox: boolean;

  /** Whether we're in an address transition */
  inAddressTransition?: boolean;
}

interface EmailAttachmentsProps {
  /** List of attachments */
  attachments: {
    filename: string;
    size: number;
    contentType: string;
  }[];

  /** Function to format file size */
  formatFileSize: (bytes: number) => string;
}

/**
 * EmailAttachments Component
 *
 * Displays a list of email attachments with file information.
 */
function EmailAttachments({ attachments, formatFileSize }: EmailAttachmentsProps) {
  return (
    <div className="px-6 sm:px-8 py-4 mt-4 border-t border-neutral-100 pt-4">
      <h3 className="text-sm font-medium text-gray-700 mb-2">Attachments ({attachments.length})</h3>
      <ul className="space-y-2">
        {attachments.map((attachment, index) => (
          <li key={index} className="flex items-center p-2 bg-neutral-50 rounded-md">
            <div className="flex-shrink-0 mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-neutral-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-neutral-800 truncate">{attachment.filename}</p>
              <p className="text-xs text-neutral-500">{formatFileSize(attachment.size)} • {attachment.contentType}</p>
            </div>
            <div className="ml-2">
              <button
                type="button"
                className="inline-flex items-center px-2.5 py-1.5 border border-neutral-200 shadow-sm text-xs font-medium rounded text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Download
              </button>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}

/**
 * AddressTransitionState Component
 *
 * Displays a message during address transition
 */
function AddressTransitionState() {
  return (
    <div className="flex flex-col items-center justify-center h-full px-6 sm:px-8 py-8 text-center">
      <div className="mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-[#ce601c] animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-gray-900">Generating New Address</h3>
      <p className="mt-1 text-sm text-gray-500">Please wait while we set up your new inbox...</p>
      <div className="mt-4">
        <div className="flex space-x-2">
          <div className="w-3 h-3 bg-[#ce601c] rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-3 h-3 bg-[#ce601c] rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-3 h-3 bg-[#ce601c] rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
        </div>
      </div>
    </div>
  );
}

/**
 * NoEmailSelected Component
 *
 * Displays a message when no email is selected.
 */
function NoEmailSelected() {
  return (
    <div className="flex flex-col items-center justify-center h-full px-6 sm:px-8 py-8 text-center">
      <div className="mb-4">
        <AnimatedEnvelope />
      </div>
      <h3 className="text-lg font-medium text-gray-900">No Email Selected</h3>
      <p className="mt-1 text-sm text-gray-500">Select an email from the list to view its contents.</p>
    </div>
  );
}

/**
 * SimpleIframeEmailViewer Component
 *
 * Displays the content of a selected email using a simple iframe renderer,
 * including sender information, subject, body, and attachments.
 * Adapts to mobile and desktop views.
 */
export default function SimpleIframeEmailViewer({
  selectedEmail,
  emailAddress,
  isLoading,
  formatFileSize,
  onBackToInbox,
  showMobileInbox,
  inAddressTransition = false
}: SimpleIframeEmailViewerProps) {
  const [isMounted, setIsMounted] = useState(false);
  // State to track if we're on mobile
  const [isMobile, setIsMobile] = useState(false);

  // Handle client-side rendering
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Effect to check window size on mount and resize
  useEffect(() => {
    // Function to check if we're on mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640);
    };

    // Check initially
    if (typeof window !== 'undefined') {
      checkMobile();

      // Add resize listener
      window.addEventListener('resize', checkMobile);

      // Clean up
      return () => window.removeEventListener('resize', checkMobile);
    }
  }, []);

  // Format the date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    }).format(date);
  };

  return (
    <div
      className={`email-viewer flex-1 flex flex-col bg-white overflow-hidden transition-all duration-300 ease-in-out md:border-l ${
        showMobileInbox ? 'hidden md:flex' : 'flex'
      }`}
      style={{ borderColor: 'var(--earth-beige-secondary)' }}
    >
      {inAddressTransition ? (
        <AddressTransitionState />
      ) : selectedEmail ? (
        <div className="flex flex-col h-full overflow-hidden">
          {/* Mobile back button */}
          <div className="md:hidden p-3 border-b" style={{ borderColor: 'var(--earth-beige-secondary)' }}>
            <button
              onClick={onBackToInbox}
              className="flex items-center text-sm text-[#ce601c] hover:text-[#b85518] transition-colors duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
              Back to Inbox
            </button>
          </div>

          {/* Email header */}
          <div className="px-6 sm:px-8 py-4 sm:py-6 border-b" style={{ borderColor: 'var(--earth-beige-secondary)' }}>
            <div className="mb-2">
              <h2 className="text-xl font-semibold break-words" style={{ color: 'var(--earth-brown-dark)' }}>{selectedEmail.subject}</h2>
            </div>
            <div className="flex flex-wrap items-center text-sm mb-1" style={{ color: 'var(--earth-brown-medium)' }}>
              <span className="mr-2 font-medium" style={{ color: 'var(--earth-brown-dark)' }}>From:</span>
              <span className="mr-4">{selectedEmail.fromName} &lt;{selectedEmail.fromEmail}&gt;</span>
            </div>
            <div className="flex flex-wrap items-center text-sm mb-1" style={{ color: 'var(--earth-brown-medium)' }}>
              <span className="mr-2 font-medium" style={{ color: 'var(--earth-brown-dark)' }}>To:</span>
              <span className="mr-4">{emailAddress}</span>
            </div>
            <div className="flex justify-end text-sm" style={{ color: 'var(--earth-brown-medium)' }}>
              <span>{formatDate(selectedEmail.date)}</span>
            </div>
          </div>

          {/* Email Body using Simple Iframe renderer */}
          <div
            className="px-6 sm:px-8 py-5 sm:py-6 overflow-visible will-change-contents"
            style={{
              minHeight: '200px',
              maxHeight: selectedEmail.id.startsWith('guide-') ? (isMobile ? 'none' : '550px') : 'none', // Responsive height for guide emails
              position: 'relative',
              width: '100%',
              transition: 'height 0.3s cubic-bezier(0.16, 1, 0.3, 1)',
              overflowX: 'auto', // Allow horizontal scrolling
              overflowY: selectedEmail.id.startsWith('guide-') ? (isMobile ? 'auto' : 'hidden') : 'visible', // Allow scrolling on mobile for guide emails
              WebkitOverflowScrolling: 'touch',
              msOverflowStyle: '-ms-autohiding-scrollbar'
            }}
          >
            {isMounted && (
              <SimpleIframeRenderer
                html={selectedEmail.html}
                className="w-full"
                style={{
                  minHeight: '600px',
                  width: '100%',
                  display: 'block',
                  maxWidth: '100%',
                  borderRadius: '0.375rem'
                }}
                isGuideEmail={selectedEmail.id.startsWith('guide-')}
              />
            )}
          </div>

          {/* Attachments */}
          {selectedEmail.attachments.length > 0 && (
            <EmailAttachments
              attachments={selectedEmail.attachments}
              formatFileSize={formatFileSize}
            />
          )}
        </div>
      ) : (
        <NoEmailSelected />
      )}
    </div>
  );
}
