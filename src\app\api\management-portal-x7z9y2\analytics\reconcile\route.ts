/**
 * Analytics Reconciliation API Endpoint
 * 
 * This endpoint reconciles session analytics with actual analytics events
 * to fix discrepancies between event tracking and session aggregation.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { reconcileSessionAnalytics } from '@/lib/analytics/sessionManager';
import { logError, logInfo } from '@/lib/logging';

/**
 * POST /api/management-portal-x7z9y2/analytics/reconcile
 * Reconcile session analytics with actual events
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { sessionId, reconcileAll = false } = body;

    logInfo('AnalyticsReconciliation', 'Starting analytics reconciliation', { sessionId, reconcileAll });

    const supabase = createServerSupabaseClient();
    let reconciledSessions = 0;
    let failedSessions = 0;

    if (sessionId) {
      // Reconcile specific session
      const success = await reconcileSessionAnalytics(sessionId);
      if (success) {
        reconciledSessions = 1;
      } else {
        failedSessions = 1;
      }
    } else if (reconcileAll) {
      // Reconcile all sessions
      const { data: sessions, error: sessionsError } = await supabase
        .from('session_analytics')
        .select('session_id');

      if (sessionsError) {
        logError('AnalyticsReconciliation', 'Failed to fetch sessions for reconciliation', { error: sessionsError });
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to fetch sessions for reconciliation',
            details: sessionsError.message
          },
          { status: 500 }
        );
      }

      // Reconcile each session
      for (const session of sessions || []) {
        const success = await reconcileSessionAnalytics(session.session_id);
        if (success) {
          reconciledSessions++;
        } else {
          failedSessions++;
        }
      }
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Either sessionId or reconcileAll=true must be provided'
        },
        { status: 400 }
      );
    }

    const result = {
      success: true,
      message: 'Analytics reconciliation completed',
      results: {
        reconciledSessions,
        failedSessions,
        totalProcessed: reconciledSessions + failedSessions
      },
      timestamp: new Date().toISOString()
    };

    logInfo('AnalyticsReconciliation', 'Analytics reconciliation completed', result);

    return NextResponse.json(result);

  } catch (error) {
    logError('AnalyticsReconciliation', 'Unexpected error during reconciliation', { error });
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Unexpected error occurred during reconciliation',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/management-portal-x7z9y2/analytics/reconcile
 * Check for discrepancies between events and session analytics
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    const supabase = createServerSupabaseClient();
    const discrepancies = [];

    if (sessionId) {
      // Check specific session
      const discrepancy = await checkSessionDiscrepancy(sessionId);
      if (discrepancy) {
        discrepancies.push(discrepancy);
      }
    } else {
      // Check all sessions (limit to recent ones for performance)
      const { data: sessions, error: sessionsError } = await supabase
        .from('session_analytics')
        .select('session_id')
        .order('session_start_time', { ascending: false })
        .limit(100);

      if (sessionsError) {
        logError('AnalyticsReconciliation', 'Failed to fetch sessions for discrepancy check', { error: sessionsError });
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to fetch sessions for discrepancy check',
            details: sessionsError.message
          },
          { status: 500 }
        );
      }

      // Check each session for discrepancies
      for (const session of sessions || []) {
        const discrepancy = await checkSessionDiscrepancy(session.session_id);
        if (discrepancy) {
          discrepancies.push(discrepancy);
        }
      }
    }

    return NextResponse.json({
      success: true,
      discrepancies,
      totalChecked: sessionId ? 1 : 100,
      discrepanciesFound: discrepancies.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logError('AnalyticsReconciliation', 'Unexpected error checking discrepancies', { error });
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Unexpected error occurred while checking discrepancies',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * Helper function to check for discrepancies in a specific session
 */
async function checkSessionDiscrepancy(sessionId: string) {
  try {
    const supabase = createServerSupabaseClient();

    // Get session analytics
    const { data: sessionData, error: sessionError } = await supabase
      .from('session_analytics')
      .select('*')
      .eq('session_id', sessionId)
      .single();

    if (sessionError || !sessionData) {
      return null;
    }

    // Get actual events
    const { data: events, error: eventsError } = await supabase
      .from('analytics_events')
      .select('event_type')
      .eq('session_id', sessionId);

    if (eventsError) {
      return null;
    }

    // Count events by type
    const eventCounts = events?.reduce((acc, event) => {
      acc[event.event_type] = (acc[event.event_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Check for discrepancies
    const discrepancies = [];

    if ((eventCounts['email_address_generated'] || 0) !== (sessionData.emails_generated_count || 0)) {
      discrepancies.push({
        metric: 'emails_generated',
        events: eventCounts['email_address_generated'] || 0,
        session: sessionData.emails_generated_count || 0
      });
    }

    if ((eventCounts['email_received'] || 0) !== (sessionData.emails_received_count || 0)) {
      discrepancies.push({
        metric: 'emails_received',
        events: eventCounts['email_received'] || 0,
        session: sessionData.emails_received_count || 0
      });
    }

    if ((eventCounts['email_opened'] || 0) !== (sessionData.emails_viewed_count || 0)) {
      discrepancies.push({
        metric: 'emails_viewed',
        events: eventCounts['email_opened'] || 0,
        session: sessionData.emails_viewed_count || 0
      });
    }

    if ((eventCounts['email_address_copied'] || 0) !== (sessionData.copy_actions_count || 0)) {
      discrepancies.push({
        metric: 'copy_actions',
        events: eventCounts['email_address_copied'] || 0,
        session: sessionData.copy_actions_count || 0
      });
    }

    if (discrepancies.length > 0) {
      return {
        sessionId,
        discrepancies,
        eventCounts,
        sessionData: {
          emails_generated: sessionData.emails_generated_count,
          emails_received: sessionData.emails_received_count,
          emails_viewed: sessionData.emails_viewed_count,
          copy_actions: sessionData.copy_actions_count
        }
      };
    }

    return null;

  } catch (error) {
    logError('AnalyticsReconciliation', `Error checking discrepancy for session ${sessionId}`, { error });
    return null;
  }
}
