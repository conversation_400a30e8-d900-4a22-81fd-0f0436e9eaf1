# Test files
**/*.test.ts
**/*.test.tsx
**/*.spec.ts
**/*.spec.tsx
test-results/
**/*test-results*/
e2e/
playwright-report/
**/__tests__/
**/__mocks__/
src/tests/
src/app/test-*/
src/app/email-rendering-test/

# Debug files and directories
**/debug/
**/debug-*/
**/debug-ad-insert/
**/test-ad-insert/
src/app/api/mock-emails/

# Test utilities
scripts/test-*.js
scripts/check-*.js
jest.config.js
jest.setup.js
jest.setup-test-env.js
playwright.config.ts

# Development configuration
.vscode/
.github/
.syncignore
.eslintrc.json
.prettierrc
.prettierignore
build-ignore-errors.js
build.js

# Documentation and Markdown files
**/*.md
docs/
performance/
security/

# Other files not needed in production
LICENSE
.augment-guidelines

# Dependencies and build artifacts
node_modules/
.next/
.swc/

# Cache and temporary files
.vercel/
.cache/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Local environment files (except .env.local.example which should be included)
.env
.env.local
.env.development
.env.test

# DO NOT exclude these critical files
!tsconfig.json
!package.json
!package-lock.json
!next.config.js
!next.config.mjs
!postcss.config.mjs
!tailwind.config.js
!.env.local.example
!public/
!src/app/
!src/components/
!src/hooks/
!src/lib/
!src/middleware.ts
!src/types/
