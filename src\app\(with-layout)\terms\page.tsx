import React from 'react';
import Link from 'next/link';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Terms of Service | VanishPost',
  description: 'VanishPost terms of service - rules and guidelines for using our temporary email service.',
};

export default function TermsOfServicePage() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-12 sm:py-16">
      <div className="text-center mb-12">
        <h1 className="text-3xl sm:text-4xl font-bold mb-4" style={{ color: 'var(--earth-brown-dark)' }}>Terms of Service</h1>
        <p className="text-lg max-w-3xl mx-auto" style={{ color: 'var(--earth-brown-medium)' }}>
          Last updated: October 1, 2024
        </p>
      </div>

      <div className="p-8 rounded-lg shadow-sm" style={{ backgroundColor: 'var(--earth-beige-light)', border: '1px solid var(--earth-beige-secondary)' }}>
        <div className="max-w-none" style={{ color: 'var(--earth-brown-dark)' }}>
          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>1. Introduction</h2>
          <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
            Welcome to VanishPost ("we," "our," or "us"). By accessing or using our temporary email service at vanishpost.com (the "Service"), you agree to be bound by these Terms of Service ("Terms"). If you disagree with any part of these Terms, you may not access the Service.
          </p>

          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>2. Description of Service</h2>
          <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
            VanishPost provides temporary, disposable email addresses that automatically expire after 15 minutes. The Service allows users to receive emails without providing their personal email addresses to third parties.
          </p>

          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>3. User Responsibilities</h2>
          <p className="mb-4" style={{ color: 'var(--earth-brown-medium)' }}>
            By using our Service, you agree to:
          </p>
          <ul className="list-disc pl-6 mb-6 space-y-2" style={{ color: 'var(--earth-brown-medium)' }}>
            <li>Use the Service in compliance with all applicable laws and regulations</li>
            <li>Not use the Service for any illegal activities, including but not limited to fraud, phishing, or harassment</li>
            <li>Not attempt to circumvent any limitations or security measures implemented by the Service</li>
            <li>Not use the Service to distribute malware, spam, or other harmful content</li>
            <li>Not interfere with or disrupt the integrity or performance of the Service</li>
            <li>Not collect or harvest any information from the Service except as expressly permitted</li>
          </ul>

          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>4. Intellectual Property</h2>
          <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
            The Service and its original content, features, and functionality are and will remain the exclusive property of VanishPost and its licensors. The Service is protected by copyright, trademark, and other laws of both the United States and foreign countries. Our trademarks and trade dress may not be used in connection with any product or service without the prior written consent of VanishPost.
          </p>

          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>5. Limitation of Liability</h2>
          <p className="mb-4" style={{ color: 'var(--earth-brown-medium)' }}>
            In no event shall VanishPost, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from:
          </p>
          <ul className="list-disc pl-6 mb-6 space-y-2" style={{ color: 'var(--earth-brown-medium)' }}>
            <li>Your access to or use of or inability to access or use the Service</li>
            <li>Any conduct or content of any third party on the Service</li>
            <li>Any content obtained from the Service</li>
            <li>Unauthorized access, use, or alteration of your transmissions or content</li>
            <li>The deletion of emails or email addresses after the 15-minute expiration period</li>
          </ul>

          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>6. Disclaimer</h2>
          <p className="mb-4" style={{ color: 'var(--earth-brown-medium)' }}>
            Your use of the Service is at your sole risk. The Service is provided on an "AS IS" and "AS AVAILABLE" basis. The Service is provided without warranties of any kind, whether express or implied, including, but not limited to, implied warranties of merchantability, fitness for a particular purpose, non-infringement, or course of performance.
          </p>
          <p className="mb-4" style={{ color: 'var(--earth-brown-medium)' }}>
            VanishPost does not warrant that:
          </p>
          <ul className="list-disc pl-6 mb-6 space-y-2" style={{ color: 'var(--earth-brown-medium)' }}>
            <li>The Service will function uninterrupted, secure, or available at any particular time or location</li>
            <li>Any errors or defects will be corrected</li>
            <li>The Service is free of viruses or other harmful components</li>
            <li>The results of using the Service will meet your requirements</li>
          </ul>

          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>7. Service Modifications</h2>
          <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
            We reserve the right to modify or discontinue, temporarily or permanently, the Service (or any part thereof) with or without notice. We shall not be liable to you or to any third party for any modification, suspension, or discontinuance of the Service.
          </p>

          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>8. Data Retention and Privacy</h2>
          <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
            All temporary email addresses and associated emails are automatically deleted after 15 minutes. We do not archive or backup email content beyond this period. For more information about how we collect, use, and share your information, please review our <Link href="/privacy" className="text-[#ce601c] hover:text-[#b85518]">Privacy Policy</Link>.
          </p>

          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>9. Governing Law</h2>
          <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
            These Terms shall be governed and construed in accordance with the laws of the United States, without regard to its conflict of law provisions. Our failure to enforce any right or provision of these Terms will not be considered a waiver of those rights.
          </p>

          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>10. Changes to Terms</h2>
          <p className="mb-6" style={{ color: 'var(--earth-brown-medium)' }}>
            We reserve the right, at our sole discretion, to modify or replace these Terms at any time. By continuing to access or use our Service after those revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, please stop using the Service.
          </p>

          <h2 className="text-xl font-semibold mb-3" style={{ color: 'var(--earth-brown-dark)' }}>11. Contact Us</h2>
          <p className="mb-4" style={{ color: 'var(--earth-brown-medium)' }}>
            If you have any questions about these Terms, please contact us:
          </p>
          <ul className="list-disc pl-6 mb-6 space-y-2" style={{ color: 'var(--earth-brown-medium)' }}>
            <li>By email: <EMAIL></li>
            <li>By visiting the contact page on our website: <Link href="/contact" className="text-[#ce601c] hover:text-[#b85518]">Contact Us</Link></li>
          </ul>
        </div>
      </div>

      <div className="mt-8 text-center">
        <Link href="/privacy" className="inline-block bg-[#ce601c] text-white px-6 py-3 rounded-md font-medium hover:bg-[#b85518] transition-colors">
          View our Privacy Policy
        </Link>
      </div>
    </div>
  );
}
