/**
 * API route for getting the cleanup scheduler status
 * 
 * This API provides information about the current status of the cleanup scheduler.
 */
import { NextRequest, NextResponse } from 'next/server';
import { logError } from '@/lib/logging';
import { isCleanupSchedulerRunning } from '@/lib/cleanup/cleanupScheduler';
import { getConfig } from '@/lib/config/configService';

/**
 * GET /api/admin/cleanup/status
 * 
 * Get the status of the cleanup scheduler
 */
export async function GET(request: NextRequest) {
  try {
    const running = isCleanupSchedulerRunning();
    const intervalMinutes = await getConfig('cleanupIntervalMinutes');
    
    // Calculate next run time if running
    let nextRunAt = null;
    if (running && intervalMinutes) {
      nextRunAt = new Date(Date.now() + intervalMinutes * 60 * 1000).toISOString();
    }
    
    return NextResponse.json({
      success: true,
      status: {
        running,
        intervalMinutes,
        nextRunAt
      }
    });
  } catch (error) {
    logError('cleanup', 'Error getting cleanup scheduler status', { error });
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
