/**
 * TypeScript definitions for Supabase database tables
 *
 * This file defines the types for the Supabase database tables, providing
 * type safety when interacting with the database through the Supabase client.
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      temp_emails: {
        Row: {
          id: number
          email_address: string
          creation_time: string
          expiration_date: string
        }
        Insert: {
          id?: number
          email_address: string
          creation_time?: string
          expiration_date: string
        }
        Update: {
          id?: number
          email_address?: string
          creation_time?: string
          expiration_date?: string
        }
        Relationships: []
      }
      database_configurations: {
        Row: {
          id: number
          name: string
          description: string | null
          is_active: boolean
          guerrilla_config: Json
          supabase_config: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          name: string
          description?: string | null
          is_active?: boolean
          guerrilla_config: Json
          supabase_config: J<PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          name?: string
          description?: string | null
          is_active?: boolean
          guerrilla_config?: <PERSON>son
          supabase_config?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      ad_config: {
        Row: {
          placement_id: string
          domain: string
          ad_unit_id: string
          ad_client_id: string
          is_enabled: boolean
          device_types: Json
          display_options: Json | null
          schedule: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          placement_id: string
          domain: string
          ad_unit_id: string
          ad_client_id?: string
          is_enabled?: boolean
          device_types: Json
          display_options?: Json | null
          schedule?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          placement_id?: string
          domain?: string
          ad_unit_id?: string
          ad_client_id?: string
          is_enabled?: boolean
          device_types?: Json
          display_options?: Json | null
          schedule?: Json | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      analytics_events: {
        Row: {
          id: number
          event_type: string
          page_path: string | null
          referrer: string | null
          country: string | null
          browser: string | null
          device_type: string | null
          timestamp: string
          additional_data: Json | null
        }
        Insert: {
          id?: number
          event_type: string
          page_path?: string | null
          referrer?: string | null
          country?: string | null
          browser?: string | null
          device_type?: string | null
          timestamp?: string
          additional_data?: Json | null
        }
        Update: {
          id?: number
          event_type?: string
          page_path?: string | null
          referrer?: string | null
          country?: string | null
          browser?: string | null
          device_type?: string | null
          timestamp?: string
          additional_data?: Json | null
        }
        Relationships: []
      }

      app_config: {
        Row: {
          key: string
          value: Json
          updated_at: string
        }
        Insert: {
          key: string
          value: Json
          updated_at?: string
        }
        Update: {
          key?: string
          value?: Json
          updated_at?: string
        }
        Relationships: []
      }
      domain_config: {
        Row: {
          domain: string
          is_active: boolean
          settings: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          domain: string
          is_active?: boolean
          settings: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          domain?: string
          is_active?: boolean
          settings?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      system_logs: {
        Row: {
          id: number
          level: string
          category: string
          message: string
          metadata: Json | null
          timestamp: string
        }
        Insert: {
          id?: number
          level: string
          category: string
          message: string
          metadata?: Json | null
          timestamp?: string
        }
        Update: {
          id?: number
          level?: string
          category?: string
          message?: string
          metadata?: Json | null
          timestamp?: string
        }
        Relationships: []
      }
      contact_messages: {
        Row: {
          id: number
          name: string
          email: string
          subject: string
          message: string
          status: string
          thread_id: string | null
          parent_id: number | null
          is_admin_reply: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          name: string
          email: string
          subject: string
          message: string
          status?: string
          thread_id?: string | null
          parent_id?: number | null
          is_admin_reply?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          name?: string
          email?: string
          subject?: string
          message?: string
          status?: string
          thread_id?: string | null
          parent_id?: number | null
          is_admin_reply?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contact_messages_parent_id_fkey"
            columns: ["parent_id"]
            referencedRelation: "contact_messages"
            referencedColumns: ["id"]
          }
        ]
      }
      generated_records: {
        Row: {
          id: string
          session_id: string
          user_id: string | null
          record_type: string
          domain: string
          selector: string | null
          dns_record: string
          record_name: string
          private_key_encrypted: string | null
          metadata: Json
          created_at: string
          expires_at: string
          validated_at: string | null
          validation_status: string
          validation_errors: Json
        }
        Insert: {
          id?: string
          session_id: string
          user_id?: string | null
          record_type: string
          domain: string
          selector?: string | null
          dns_record: string
          record_name: string
          private_key_encrypted?: string | null
          metadata?: Json
          created_at?: string
          expires_at?: string
          validated_at?: string | null
          validation_status?: string
          validation_errors?: Json
        }
        Update: {
          id?: string
          session_id?: string
          user_id?: string | null
          record_type?: string
          domain?: string
          selector?: string | null
          dns_record?: string
          record_name?: string
          private_key_encrypted?: string | null
          metadata?: Json
          created_at?: string
          expires_at?: string
          validated_at?: string | null
          validation_status?: string
          validation_errors?: Json
        }
        Relationships: []
      }
      dns_validation_history: {
        Row: {
          id: string
          record_id: string | null
          validation_type: string
          domain: string
          record_name: string
          expected_value: string | null
          actual_values: Json
          is_valid: boolean
          errors: Json
          validated_at: string
        }
        Insert: {
          id?: string
          record_id?: string | null
          validation_type: string
          domain: string
          record_name: string
          expected_value?: string | null
          actual_values?: Json
          is_valid: boolean
          errors?: Json
          validated_at?: string
        }
        Update: {
          id?: string
          record_id?: string | null
          validation_type?: string
          domain?: string
          record_name?: string
          expected_value?: string | null
          actual_values?: Json
          is_valid?: boolean
          errors?: Json
          validated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "dns_validation_history_record_id_fkey"
            columns: ["record_id"]
            referencedRelation: "generated_records"
            referencedColumns: ["id"]
          }
        ]
      }
      tool_audit_log: {
        Row: {
          id: string
          session_id: string | null
          user_id: string | null
          tool_name: string
          action: string
          resource_type: string | null
          resource_id: string | null
          metadata: Json
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: string
          session_id?: string | null
          user_id?: string | null
          tool_name: string
          action: string
          resource_type?: string | null
          resource_id?: string | null
          metadata?: Json
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          session_id?: string | null
          user_id?: string | null
          tool_name?: string
          action?: string
          resource_type?: string | null
          resource_id?: string | null
          metadata?: Json
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Relationships: []
      }
      session_analytics: {
        Row: {
          id: number
          session_id: string
          user_id: string | null
          session_start_time: string
          session_end_time: string | null
          session_duration: number | null
          emails_generated_count: number | null
          emails_received_count: number | null
          emails_viewed_count: number | null
          emails_deleted_count: number | null
          manual_refresh_count: number | null
          copy_actions_count: number | null
          address_regeneration_count: number | null
          device_type: string | null
          browser: string | null
          country: string | null
          referrer: string | null
          created_at: string | null
          updated_at: string | null
          last_seen_at: string | null
          is_active: boolean | null
        }
        Insert: {
          id?: number
          session_id: string
          user_id?: string | null
          session_start_time: string
          session_end_time?: string | null
          session_duration?: number | null
          emails_generated_count?: number | null
          emails_received_count?: number | null
          emails_viewed_count?: number | null
          emails_deleted_count?: number | null
          manual_refresh_count?: number | null
          copy_actions_count?: number | null
          address_regeneration_count?: number | null
          device_type?: string | null
          browser?: string | null
          country?: string | null
          referrer?: string | null
          created_at?: string | null
          updated_at?: string | null
          last_seen_at?: string | null
          is_active?: boolean | null
        }
        Update: {
          id?: number
          session_id?: string
          user_id?: string | null
          session_start_time?: string
          session_end_time?: string | null
          session_duration?: number | null
          emails_generated_count?: number | null
          emails_received_count?: number | null
          emails_viewed_count?: number | null
          emails_deleted_count?: number | null
          manual_refresh_count?: number | null
          copy_actions_count?: number | null
          address_regeneration_count?: number | null
          device_type?: string | null
          browser?: string | null
          country?: string | null
          referrer?: string | null
          created_at?: string | null
          updated_at?: string | null
          last_seen_at?: string | null
          is_active?: boolean | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      execute_sql: {
        Args: {
          sql: string
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
