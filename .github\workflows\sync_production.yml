
name: Sync to Production Repository
on:
  # Manual trigger
  workflow_dispatch:
  # Automatically trigger when pushing to master branch
  # Uncomment the lines below when you're ready for automatic syncing
  # push:
  #   branches:
  #     - master
jobs:
  sync-to-production:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Set up Git
        run: |
          git config --global user.name 'GitHub Action'
          git config --global user.email '<EMAIL>'
      - name: Sync to production repository
        env:
          TARGET_REPO: ${{ secrets.PRODUCTION_REPO_URL_JSV }}
          PAT: ${{ secrets.PRODUCTION_REPO_TOKEN_JSV }}
        run: |
          echo "Starting sync to production repository"

          # Verify environment variables
          if [ -z "$TARGET_REPO" ]; then
            echo "::error::TARGET_REPO is not set. Please check the PRODUCTION_REPO_URL_JSV secret."
            exit 1
          fi

          if [ -z "$PAT" ]; then
            echo "::error::PAT is not set. Please check the PRODUCTION_REPO_TOKEN_JSV secret."
            exit 1
          fi

          # Create a list of files to exclude
          cat > .syncignore << EOL
          # Test files
          **/*.test.ts
          **/*.test.tsx
          **/*.spec.ts
          **/*.spec.tsx
          test-results/
          e2e/
          playwright-report/
          src/app/__tests__/
          src/components/__tests__/
          src/hooks/__tests__/
          src/lib/__tests__/
          src/lib/*/__tests__/
          src/tests/
          src/__tests__/

          # Mock files and directories
          **/__mocks__/
          src/__mocks__/
          src/lib/__mocks__/
          src/lib/*/__mocks__/
          src/app/api/mock-emails/

          # Test and debug pages
          src/app/test-*/
          src/app/email-rendering-test/

          # Debug files and directories
          **/debug/
          **/debug-*/
          **/debug-ad-insert/
          **/test-ad-insert/

          # Test utilities
          scripts/test-*.js
          scripts/check-*.js

          # Development configuration
          .vscode/
          .github/
          .syncignore
          .swc/
          jest.config.js
          jest.setup.js
          jest.setup-test-env.js
          playwright.config.ts
          build-ignore-errors.js
          build.js
          tsconfig.tsbuildinfo

          # Documentation and Markdown files
          **/*.md
          **/*docs/*.md
          docs/
          performance/
          security/
          **/*BACKEND_IMPLEMENTATION_PLAN.md
          **/*CONTRIBUTING.md
          **/*DEPLOYMENT_GUIDE.md
          **/*PROJECT_STATUS.md
          **/*README.md
          **/*ROADMAP.md
          **/*SUPABASE_MIGRATION.md
          **/*TEST_DOCUMENTATION.md
          **/*TEST_RESULTS.md
          **/*TESTING.md
          **/*auto-refresh-feature.md
          **/*security/README.md
          **/*performance/README.md
          **/* UI_implementation.md
          **/*clean-code.md
          **/*nextjs.md
          **/*react.md
          **/*react-letter.md
          **/*SECURITY.md
          **/*tailwind.md
          **/*.md

          # Other files not needed in production
          LICENSE
          .augment-guidelines

          # Dependencies and build artifacts
          node_modules/
          .next/
          .swc/

          # Cache and temporary files
          .vercel/
          .cache/
          *.log
          npm-debug.log*
          yarn-debug.log*
          yarn-error.log*
          pnpm-debug.log*

          # Local environment files (except .env.local.example which should be included)
          .env
          .env.local
          .env.development
          .env.test
          EOL

          # Clone the production repository
          echo "Cloning production repository: ${TARGET_REPO}"
          if ! git clone https://x-access-token:${PAT}@github.com/${TARGET_REPO}.git production-repo; then
            echo "::error::Failed to clone production repository. Please check the PRODUCTION_REPO_URL_JSV and PRODUCTION_REPO_TOKEN_JSV secrets."
            exit 1
          fi

          # Copy all files from source to production, excluding those in .syncignore
          echo "Copying files to production repository"
          if ! rsync -av --exclude-from=.syncignore --exclude='.git/' --exclude='production-repo/' . production-repo/; then
            echo "::error::Failed to copy files to production repository."
            exit 1
          fi

          # Go to production repository
          cd production-repo || {
            echo "::error::Failed to change directory to production-repo."
            exit 1
          }

          # Configure git
          git config user.name 'GitHub Action'
          git config user.email '<EMAIL>'

          # Add all changes
          git add -A

          # Check if there are any changes to commit
          if git diff --staged --quiet; then
            echo "No changes to commit. Production repository is already up to date."
          else
            # Commit changes
            if ! git commit -m "Sync from main repository"; then
              echo "::error::Failed to commit changes."
              exit 1
            fi

            # Push changes
            echo "Pushing changes to production repository"
            if ! git push; then
              echo "::error::Failed to push changes to production repository. This might be due to permission issues or conflicts."
              exit 1
            fi
          fi

          echo "Sync completed successfully"


