/**
 * Admin Analytics API Endpoint
 * 
 * This endpoint provides analytics data for the admin dashboard.
 * Requires authentication and provides various analytics queries.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { logError, logInfo } from '@/lib/logging';
import { analyticsCache, CacheKeys, CACHE_TTL } from '@/lib/analytics/cache';

/**
 * Validate date range parameters
 */
function validateDateRange(startDate?: string, endDate?: string): { isValid: boolean; error?: string } {
  if (startDate && isNaN(Date.parse(startDate))) {
    return { isValid: false, error: 'Invalid startDate format' };
  }
  
  if (endDate && isNaN(Date.parse(endDate))) {
    return { isValid: false, error: 'Invalid endDate format' };
  }
  
  if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
    return { isValid: false, error: 'startDate cannot be after endDate' };
  }
  
  return { isValid: true };
}

/**
 * Get time range filter based on timeRange parameter
 */
function getTimeRangeFilter(timeRange: string): { startDate: Date; endDate: Date } {
  const now = new Date();
  const endDate = new Date(now);
  let startDate: Date;

  switch (timeRange) {
    case '1h':
      startDate = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour ago
      break;
    case '24h':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago
      break;
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
      break;
    default:
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // Default to 24 hours
  }

  return { startDate, endDate };
}

/**
 * GET /api/management-portal-x7z9y2/analytics
 * Retrieve analytics data for dashboard
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '24h';
    const eventType = searchParams.get('eventType');
    const aggregation = searchParams.get('aggregation') || 'count';
    const customStartDate = searchParams.get('startDate');
    const customEndDate = searchParams.get('endDate');

    // Validate custom date range if provided
    if (customStartDate || customEndDate) {
      const validation = validateDateRange(customStartDate || undefined, customEndDate || undefined);
      if (!validation.isValid) {
        return NextResponse.json(
          { success: false, error: validation.error },
          { status: 400 }
        );
      }
    }

    // Get time range
    let startDate: Date, endDate: Date;
    if (customStartDate && customEndDate) {
      startDate = new Date(customStartDate);
      endDate = new Date(customEndDate);
    } else {
      ({ startDate, endDate } = getTimeRangeFilter(timeRange));
    }

    // Generate cache key
    const cacheKey = customStartDate && customEndDate
      ? CacheKeys.customRange(customStartDate, customEndDate, `analytics-${aggregation}`)
      : aggregation === 'timeline'
        ? CacheKeys.timelineData(timeRange, aggregation)
        : CacheKeys.aggregatedMetrics(timeRange, aggregation);

    // Try to get from cache first
    const cachedResult = analyticsCache.get<{
      data: any;
      meta: any;
      cacheTime: string;
    }>(cacheKey);
    if (cachedResult) {
      logInfo('AdminAnalyticsAPI', 'Cache hit for analytics data', {
        cacheKey,
        timeRange,
        aggregation
      });
      return NextResponse.json({
        success: true,
        data: cachedResult.data,
        meta: {
          ...cachedResult.meta,
          cached: true,
          cacheTime: cachedResult.cacheTime
        }
      });
    }

    const supabase = createServerSupabaseClient();

    // Build base query
    let query = supabase
      .from('analytics_events')
      .select('*')
      .gte('timestamp', startDate.toISOString())
      .lte('timestamp', endDate.toISOString())
      .order('timestamp', { ascending: false });

    // Add event type filter if specified
    if (eventType) {
      query = query.eq('event_type', eventType);
    }

    const { data: events, error } = await query;

    if (error) {
      logError('AdminAnalyticsAPI', 'Failed to fetch analytics events', { error });
      return NextResponse.json(
        { success: false, error: 'Failed to fetch analytics data' },
        { status: 500 }
      );
    }

    // Process data based on aggregation type
    let result: any = {};

    if (aggregation === 'count') {
      // Count events by type
      const eventCounts = events.reduce((acc: Record<string, number>, event) => {
        acc[event.event_type] = (acc[event.event_type] || 0) + 1;
        return acc;
      }, {});

      result = {
        totalEvents: events.length,
        eventCounts,
        timeRange: { startDate: startDate.toISOString(), endDate: endDate.toISOString() }
      };
    } else if (aggregation === 'timeline') {
      // Group events by hour for timeline view
      const timeline = events.reduce((acc: Record<string, Record<string, number>>, event) => {
        const hour = new Date(event.timestamp).toISOString().slice(0, 13) + ':00:00.000Z';
        if (!acc[hour]) {
          acc[hour] = {};
        }
        acc[hour][event.event_type] = (acc[hour][event.event_type] || 0) + 1;
        return acc;
      }, {});

      result = {
        timeline,
        timeRange: { startDate: startDate.toISOString(), endDate: endDate.toISOString() }
      };
    } else if (aggregation === 'sessions') {
      // TODO: Fix session_id field in analytics_events table
      // Session aggregation temporarily disabled due to missing session_id field
      result = {
        totalSessions: 0,
        sessions: [],
        timeRange: { startDate: startDate.toISOString(), endDate: endDate.toISOString() },
        note: 'Session aggregation temporarily disabled - session_id field not available'
      };
    }

    logInfo('AdminAnalyticsAPI', `Retrieved analytics data`, {
      timeRange,
      eventType,
      aggregation,
      eventCount: events.length
    });

    // Prepare response data
    const responseData = {
      data: result,
      meta: {
        timeRange,
        eventType,
        aggregation,
        totalEvents: events.length,
        queryTime: new Date().toISOString(),
        cached: false
      }
    };

    // Cache the result with appropriate TTL
    const ttl = aggregation === 'timeline'
      ? CACHE_TTL.TIMELINE_DATA
      : customStartDate && customEndDate
        ? CACHE_TTL.AGGREGATED_METRICS
        : CACHE_TTL.DASHBOARD_OVERVIEW;

    analyticsCache.set(cacheKey, {
      data: result,
      meta: responseData.meta,
      cacheTime: new Date().toISOString()
    }, ttl);

    return NextResponse.json({
      success: true,
      ...responseData
    });

  } catch (error) {
    logError('AdminAnalyticsAPI', 'Unexpected error in analytics endpoint', { error });
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/management-portal-x7z9y2/analytics
 * Create custom analytics queries
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, parameters } = body;

    if (!query) {
      return NextResponse.json(
        { success: false, error: 'Query is required' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabaseClient();

    // For security, only allow specific predefined queries
    const allowedQueries = [
      'session_summary',
      'event_summary',
      'device_breakdown',
      'browser_breakdown'
    ];

    if (!allowedQueries.includes(query)) {
      return NextResponse.json(
        { success: false, error: 'Query not allowed' },
        { status: 400 }
      );
    }

    let result: any = {};

    switch (query) {
      case 'session_summary':
        // TODO: Fix session_analytics table type definition
        // Session summary temporarily disabled due to missing table
        result = {
          totalSessions: 0,
          avgSessionDuration: 0,
          totalEmailsGenerated: 0,
          totalEmailsViewed: 0,
          sessions: [],
          note: 'Session summary temporarily disabled - session_analytics table not available'
        };
        break;

      case 'device_breakdown':
        const { data: deviceData, error: deviceError } = await supabase
          .from('analytics_events')
          .select('device_type')
          .not('device_type', 'is', null);

        if (deviceError) {
          throw deviceError;
        }

        const deviceCounts = deviceData.reduce((acc: Record<string, number>, event) => {
          const deviceType = event.device_type || 'unknown';
          acc[deviceType] = (acc[deviceType] || 0) + 1;
          return acc;
        }, {});

        result = { deviceBreakdown: deviceCounts };
        break;

      case 'browser_breakdown':
        const { data: browserData, error: browserError } = await supabase
          .from('analytics_events')
          .select('browser')
          .not('browser', 'is', null);

        if (browserError) {
          throw browserError;
        }

        const browserCounts = browserData.reduce((acc: Record<string, number>, event) => {
          const browser = event.browser || 'unknown';
          acc[browser] = (acc[browser] || 0) + 1;
          return acc;
        }, {});

        result = { browserBreakdown: browserCounts };
        break;
    }

    return NextResponse.json({
      success: true,
      data: result,
      meta: {
        query,
        parameters,
        queryTime: new Date().toISOString()
      }
    });

  } catch (error) {
    logError('AdminAnalyticsAPI', 'Error in custom analytics query', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to execute query' },
      { status: 500 }
    );
  }
}
