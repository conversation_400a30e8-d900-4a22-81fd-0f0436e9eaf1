'use client';

/**
 * StructuredData Component
 *
 * Adds JSON-LD structured data to pages for better SEO and rich snippets
 */
export default function StructuredData({ type }: { type: 'website' | 'organization' | 'faq' }) {
  // Base website structured data
  const websiteData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'VanishPost',
    url: 'https://vanishpost.com',
    description: 'Generate secure temporary email addresses that expire in 15 minutes. Protect your privacy with VanishPost\'s disposable email service.',
    potentialAction: {
      '@type': 'SearchAction',
      'target': {
        '@type': 'EntryPoint',
        'urlTemplate': 'https://vanishpost.com/search?q={search_term_string}'
      },
      'query-input': 'required name=search_term_string'
    }
  };

  // Organization structured data
  const organizationData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'VanishPost',
    url: 'https://vanishpost.com',
    logo: 'https://vanishpost.com/vanishpost-temporary-email-logo.svg',
    sameAs: [
      'https://twitter.com/vanishpost',
      'https://facebook.com/vanishpost',
      'https://instagram.com/vanishpost',
      'https://linkedin.com/company/vanishpost'
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      email: '<EMAIL>',
      contactType: 'customer service'
    }
  };

  // FAQ structured data
  const faqData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: [
      {
        '@type': 'Question',
        name: 'What is VanishPost?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'VanishPost is a free service that provides temporary, disposable email addresses that automatically expire after 15 minutes. It helps protect your privacy by allowing you to receive emails without revealing your personal email address.'
        }
      },
      {
        '@type': 'Question',
        name: 'How long do VanishPost addresses last?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'All VanishPost addresses automatically expire after 15 minutes from the time they are created. After expiration, the address and all associated emails are permanently deleted from our servers.'
        }
      },
      {
        '@type': 'Question',
        name: 'Is VanishPost completely free?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Yes, VanishPost is completely free to use. We support the service through minimal, non-intrusive advertisements that don\'t track your activity or compromise your privacy.'
        }
      }
    ]
  };

  let structuredData;

  switch (type) {
    case 'website':
      structuredData = websiteData;
      break;
    case 'organization':
      structuredData = organizationData;
      break;
    case 'faq':
      structuredData = faqData;
      break;
    default:
      structuredData = websiteData;
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
